from django.db import models
from django.contrib.auth.models import AbstractUser
import base64
from django.core.files.base import ContentFile
import random
from django.utils import timezone
from datetime import timedelta

class CustomUser(AbstractUser):
    USER_TYPE_CHOICES = (
        ('admin', 'Admin'),
        ('user', 'User'),
        ('specialist', 'Specialist'),
    )
    
    CATEGORY_CHOICES = (
        ('networking', 'Networking'),
        ('hardware', 'Hardware'),
        ('software', 'Software'),
        ('database', 'Database'),
        ('security', 'Security'),
    )
    
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, blank=True, null=True)
    phone_number = models.CharField(max_length=15, blank=True)
    address = models.TextField(blank=True)
    bio = models.TextField(blank=True, help_text="Tell us about yourself")
    
    # Store image as binary data in the database
    profile_picture = models.BinaryField(blank=True, null=True)
    profile_picture_type = models.Char<PERSON>ield(max_length=20, blank=True, null=True)  # To store the image MIME type
    
    def __str__(self):
        # Don't access bio field here
        return f"{self.username} ({self.get_user_type_display()})"
    
    def get_profile_picture_base64(self):
        """Return the profile picture as a base64 encoded string for HTML display"""
        if self.profile_picture:
            return f"data:{self.profile_picture_type};base64,{base64.b64encode(self.profile_picture).decode('utf-8')}"
        return None
    
    def set_profile_picture(self, image_file):
        """Set the profile picture from an uploaded file"""
        if image_file:
            # Read the file content
            image_data = image_file.read()
            # Store the image data and type
            self.profile_picture = image_data
            self.profile_picture_type = image_file.content_type
            return True
        return False

    def get_average_rating(self):
        """Calculate and return the average rating for this specialist"""
        if self.user_type != 'specialist':
            return None

        from app_posts.models import SpecialistRating
        ratings = SpecialistRating.objects.filter(specialist=self)

        if not ratings.exists():
            return None

        total_rating = sum(rating.rating for rating in ratings)
        return round(total_rating / ratings.count(), 1)

    def get_total_ratings_count(self):
        """Get the total number of ratings received by this specialist"""
        if self.user_type != 'specialist':
            return 0

        from app_posts.models import SpecialistRating
        return SpecialistRating.objects.filter(specialist=self).count()

    def get_completed_jobs_count(self):
        """Get the number of completed jobs for this specialist"""
        if self.user_type != 'specialist':
            return 0

        from app_posts.models import SpecialistApplication
        return SpecialistApplication.objects.filter(
            specialist=self,
            post__status='completed'
        ).count()

    class Meta:
        db_table = 'users'

class PasswordResetOTP(models.Model):
    """Model to store OTP codes for password reset"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    otp_code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)
    
    def __str__(self):
        return f"OTP for {self.user.username}"
    
    def is_valid(self):
        """Check if the OTP is still valid (not expired)"""
        from django.utils import timezone
        from datetime import timedelta
        
        # OTP is valid for 10 minutes
        expiry_time = self.created_at + timedelta(minutes=10)
        return timezone.now() <= expiry_time
    
    @classmethod
    def generate_otp(cls, user):
        """Generate a new OTP code for a user"""
        # Delete any existing unused OTPs for this user
        cls.objects.filter(user=user, is_used=False).delete()
        
        # Generate a random 6-digit OTP
        import random
        otp_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        
        # Create and return the OTP object
        return cls.objects.create(user=user, otp_code=otp_code)

    class Meta:
        db_table = 'password_reset_otps'




