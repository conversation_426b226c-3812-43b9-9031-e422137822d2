from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from .models import Notification

@login_required
def notifications_list(request):
    """View to display all notifications for the current user with pagination"""
    notifications_list = Notification.objects.filter(recipient=request.user)
    unread_count = notifications_list.filter(is_read=False).count()

    # Set up pagination - show 6 notifications per page
    paginator = Paginator(notifications_list, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'notifications': page_obj,
        'page_obj': page_obj,
        'unread_count': unread_count
    }
    return render(request, 'app_notifications/notifications_list.html', context)

@login_required
def mark_as_read(request, notification_id):
    """Mark a notification as read"""
    notification = get_object_or_404(Notification, id=notification_id, recipient=request.user)
    notification.is_read = True
    notification.save()
    return redirect('notifications_list')

@login_required
def mark_all_as_read(request):
    """Mark all notifications as read"""
    Notification.objects.filter(recipient=request.user, is_read=False).update(is_read=True)
    return redirect('notifications_list')

@login_required
def notification_redirect(request, notification_id):
    """Mark notification as read and redirect to related post or appropriate page"""
    notification = get_object_or_404(Notification, id=notification_id, recipient=request.user)

    # Mark the notification as read
    if not notification.is_read:
        notification.is_read = True
        notification.save()

    # Redirect based on notification type and related content
    if notification.related_post:
        # Redirect to the related maintenance post
        return redirect('post_detail', post_id=notification.related_post.id)
    elif notification.related_application:
        # Redirect to the application detail page
        return redirect('application_detail', application_id=notification.related_application.id)
    elif notification.notification_type == 'new_support_message' and hasattr(request.user, 'user_type') and request.user.user_type == 'admin':
        # Redirect admin users to support messages
        return redirect('admin_support_messages')
    else:
        # Default redirect to notifications list
        return redirect('notifications_list')