{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Apply for Maintenance Job</h4>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5>{{ post.title }}</h5>
                        <div class="d-flex flex-wrap gap-2 mb-2">
                            <span class="badge bg-info">{{ post.get_category_display }}</span>
                            <span class="badge bg-secondary">Experience: {{ post.get_experience_display }}</span>
                            <span class="badge bg-warning">Deadline: {{ post.deadline|date:"M d, Y" }}</span>
                        </div>
                        <p>{{ post.description }}</p>
                        {% if post.location %}
                        <p><i class="fas fa-map-marker-alt me-2"></i><strong>Location:</strong> {{ post.location }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">Apply for: {{ post.title }}</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> You are applying for a maintenance job in the <strong>{{ post.get_category_display }}</strong> category.
                                    </div>
                                    
                                    <form method="post" enctype="multipart/form-data" novalidate>
                                        {% csrf_token %}
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.message.id_for_label }}" class="form-label">Your Message</label>
                                            {{ form.message.errors }}
                                            <textarea name="{{ form.message.name }}" id="{{ form.message.id_for_label }}" 
                                                      class="form-control {% if form.message.errors %}is-invalid{% endif %}" 
                                                      rows="4" required>{{ form.message.value|default:'' }}</textarea>
                                            <div class="form-text">Explain why you're a good fit for this job and your relevant experience</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.cv_file.id_for_label }}" class="form-label">CV/Resume</label>
                                            {{ form.cv_file.errors }}
                                            <input type="file" name="{{ form.cv_file.name }}" id="{{ form.cv_file.id_for_label }}" 
                                                   class="form-control {% if form.cv_file.errors %}is-invalid{% endif %}" 
                                                   accept=".pdf,.doc,.docx" required>
                                            <div class="form-text">{{ form.cv_file.help_text }}</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.business_license.id_for_label }}" class="form-label">Business License (if applicable)</label>
                                            {{ form.business_license.errors }}
                                            <input type="file" name="{{ form.business_license.name }}" id="{{ form.business_license.id_for_label }}" 
                                                   class="form-control {% if form.business_license.errors %}is-invalid{% endif %}" 
                                                   accept=".pdf">
                                            <div class="form-text">{{ form.business_license.help_text }}</div>
                                        </div>
                                        
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <a href="{% url 'post_detail' post.id %}" class="btn btn-secondary me-md-2">Cancel</a>
                                            <button type="submit" class="btn btn-primary">Submit Application</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
