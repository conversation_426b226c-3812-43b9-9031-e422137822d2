{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card mt-5">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Set New Password</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">New Password</label>
                            {{ form.new_password1 }}
                            <div class="form-text">
                                <ul>
                                    <li>Your password can't be too similar to your other personal information.</li>
                                    <li>Your password must contain at least 8 characters.</li>
                                    <li>Your password can't be a commonly used password.</li>
                                    <li>Your password can't be entirely numeric.</li>
                                </ul>
                            </div>
                            {% if form.new_password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.new_password1.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">Confirm New Password</label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.new_password2.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
