document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation before submitting the reply
    const form = document.getElementById('supportmessage_form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const adminReply = document.getElementById('id_admin_reply').value;
            const originalReply = document.getElementById('id_admin_reply').getAttribute('data-original');
            
            // If this is a new reply or the reply has been changed
            if (adminReply && adminReply !== originalReply) {
                if (!confirm('Are you sure you want to send this reply? The user will be notified by email.')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
        
        // Store the original reply value for comparison
        const adminReplyField = document.getElementById('id_admin_reply');
        if (adminReplyField) {
            adminReplyField.setAttribute('data-original', adminReplyField.value);
        }
    }
    
    // Add quick reply templates
    const adminReplyField = document.getElementById('id_admin_reply');
    if (adminReplyField) {
        const templateContainer = document.createElement('div');
        templateContainer.className = 'quick-reply-templates';
        templateContainer.innerHTML = `
            <p style="margin-top: 10px; font-weight: bold;">Quick Reply Templates:</p>
            <button type="button" class="button" data-template="Thank you for your message. We're looking into this issue and will get back to you shortly.">Acknowledgment</button>
            <button type="button" class="button" data-template="This issue has been resolved. Please let us know if you need any further assistance.">Resolution</button>
            <button type="button" class="button" data-template="We need more information to help you with this issue. Could you please provide more details about...">Request Info</button>
        `;
        
        adminReplyField.parentNode.appendChild(templateContainer);
        
        // Add event listeners to template buttons
        const templateButtons = templateContainer.querySelectorAll('button');
        templateButtons.forEach(button => {
            button.addEventListener('click', function() {
                const template = this.getAttribute('data-template');
                adminReplyField.value = template;
                adminReplyField.focus();
            });
        });
    }
});