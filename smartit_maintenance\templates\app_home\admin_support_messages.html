{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Support Messages</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/admin_sidebar.html' with active_tab='admin_support' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            {% if support_messages %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">All Support Messages</h4>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Subject</th>
                                        <th>User</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in support_messages %}
                                        <tr class="{% if not message.admin_reply %}table-warning{% elif message.is_resolved %}table-success{% endif %}">
                                            <td>{{ message.subject }}</td>
                                            <td>
                                                <strong>{{ message.user.username }}</strong><br>
                                                <small class="text-muted">{{ message.user.email }}</small>
                                            </td>
                                            <td>{{ message.created_at|date:"M d, Y" }}</td>
                                            <td>
                                                {% if not message.admin_reply %}
                                                    <span class="badge bg-warning">Awaiting Reply</span>
                                                {% elif message.is_resolved %}
                                                    <span class="badge bg-success">Resolved</span>
                                                {% else %}
                                                    <span class="badge bg-info">Replied</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'admin:app_home_supportmessage_change' message.id %}" class="btn btn-sm btn-primary">
                                                    {% if not message.admin_reply %}Reply{% else %}View/Edit{% endif %}
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <p class="mb-0">There are no support messages yet.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}