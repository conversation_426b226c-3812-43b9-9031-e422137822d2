from django.contrib import admin
from .models import CustomUser
from smartit_maintenance.admin import admin_site

class CustomUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'user_type', 'is_active')
    list_filter = ('user_type', 'is_active', 'date_joined')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('username',)
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(user_type__in=['client', 'specialist'])

# Register with the custom admin site
admin_site.register(CustomUser, CustomUserAdmin)

# Don't register with the default admin site to avoid duplication
# admin.site.register(CustomUser, CustomUserAdmin)


