from .utils import send_sms_verification
from django.conf import settings

def send_welcome_sms(user, phone_number):
    """
    Send a welcome SMS to a newly registered user
    
    Args:
        user: The CustomUser instance of the newly registered user
        phone_number: The user's phone number (format: +255XXXXXXXXX)
        
    Returns:
        dict: API response from the SMS service
    """
    try:
        # Format the welcome message
        message = (
            f"Welcome to Smart-IT Maintenance Portal, {user.username}! "
            f"Your account has been created successfully. "
            f"Thank you for joining us."
        )
        
        # Send the SMS using the utility function
        response = send_sms_verification(phone_number, message)
        
        # Log the response for debugging
        print(f"Welcome SMS Response: {response}")
        
        return response
    except Exception as e:
        # Log the error and return a failure response
        print(f"Welcome SMS Error: {str(e)}")
        return {"status": "error", "message": str(e)}

def send_maintenance_status_sms(phone_number, status, post_title):
    """
    Send an SMS notification about maintenance status changes
    
    Args:
        phone_number: The user's phone number (format: +255XXXXXXXXX)
        status: The new status of the maintenance post
        post_title: The title of the maintenance post
        
    Returns:
        dict: API response from the SMS service
    """
    try:
        # Map status to user-friendly message
        status_messages = {
            'created': 'has been created successfully',
            'open': 'is now open for applications',
            'pending_confirmation': 'is pending specialist confirmation',
            'in_progress': 'is now in progress',
            'pending_approval': 'has been completed and is awaiting your approval',
            'completed': 'has been successfully completed',
            'closed': 'has been closed'
        }
        
        status_text = status_messages.get(status, 'has been updated')
        
        # Format the status update message
        message = (
            f"Your maintenance request '{post_title}' {status_text}. "
            f"Login to Smart-IT Maintenance Portal for more details."
        )
        
        # Send the SMS using the utility function
        response = send_sms_verification(phone_number, message)
        
        # Log the response for debugging
        print(f"Status SMS Response: {response}")
        
        return response
    except Exception as e:
        # Log the error and return a failure response
        print(f"Status SMS Error: {str(e)}")
        return {"status": "error", "message": str(e)}

def send_specialist_status_sms(phone_number, status, post_title):
    """
    Send an SMS notification to a specialist about maintenance status changes
    
    Args:
        phone_number: The specialist's phone number (format: +255XXXXXXXXX)
        status: The new status of the maintenance post
        post_title: The title of the maintenance post
        
    Returns:
        dict: API response from the SMS service
    """
    try:
        # Map status to user-friendly message for specialists
        status_messages = {
            'pending_confirmation': 'has been assigned to you and is awaiting your confirmation',
            'in_progress': 'is now in progress',
            'pending_approval': 'is awaiting client approval',
            'completed': 'has been approved and marked as completed',
            'closed': 'has been closed by the client'
        }
        
        status_text = status_messages.get(status, 'has been updated')
        
        # Format the status update message
        message = (
            f"Maintenance request '{post_title}' {status_text}. "
            f"Login to Smart-IT Maintenance Portal for more details."
        )
        
        # Send the SMS using the utility function
        response = send_sms_verification(phone_number, message)
        
        # Log the response for debugging
        print(f"Specialist Status SMS Response: {response}")
        
        return response
    except Exception as e:
        # Log the error and return a failure response
        print(f"Specialist Status SMS Error: {str(e)}")
        return {"status": "error", "message": str(e)}

def send_specialist_assignment_sms(specialist, phone_number, post_title):
    """
    Send an SMS notification to a specialist when they are assigned to a maintenance task
    
    Args:
        specialist: The CustomUser instance of the specialist
        phone_number: The specialist's phone number (format: +255XXXXXXXXX)
        post_title: The title of the maintenance post
        
    Returns:
        dict: API response from the SMS service
    """
    # Format the assignment message
    message = (
        f"Hello {specialist.username}, "
        f"You have been assigned to a new maintenance task: '{post_title}'. "
        f"Please login to Smart-IT Maintenance Portal to view the details and begin work."
    )
    
    # Send the SMS using the utility function
    return send_sms_verification(phone_number, message)

def send_password_reset_otp(user, phone_number, otp_code):
    """
    Send password reset OTP via SMS
    
    Args:
        user (CustomUser): The user requesting password reset
        phone_number (str): The phone number to send the OTP to
        otp_code (str): The OTP code to send
        
    Returns:
        dict: API response from the SMS service
    """
    try:
        # Format the OTP message
        message = (
            f"Hello {user.username}, "
            f"Your password reset OTP for Smart-IT Maintenance Portal is: {otp_code}. "
            f"This code will expire in 10 minutes."
        )
        
        # Send the SMS using the utility function
        response = send_sms_verification(phone_number, message)
        
        # Log the response for debugging
        print(f"Password Reset OTP SMS Response: {response}")
        
        return response
    except Exception as e:
        # Log the error and return a failure response
        print(f"Password Reset OTP SMS Error: {str(e)}")
        return {"status": "error", "message": str(e)}






