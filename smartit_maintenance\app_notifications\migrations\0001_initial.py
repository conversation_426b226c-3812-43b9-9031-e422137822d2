# Generated by Django 5.2.1 on 2025-06-03 04:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_posts', '0012_alter_maintenancepost_deadline_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('application_status', 'Application Status Change'), ('post_status', 'Post Status Change'), ('new_application', 'New Application'), ('job_completed', 'Job Completed'), ('job_approved', 'Job Approved'), ('job_rejected', 'Job Rejected'), ('system', 'System Notification')], max_length=20)),
                ('title', models.Char<PERSON><PERSON>(max_length=255)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('related_application', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='app_posts.specialistapplication')),
                ('related_post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='app_posts.maintenancepost')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
