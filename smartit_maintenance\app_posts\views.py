from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic.edit import FormView
from django.urls import reverse_lazy
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Avg, Count
from .models import MaintenancePost, SpecialistApplication, SpecialistRating
from .forms import MaintenancePostForm, SpecialistApplicationForm, PostSearchForm, SpecialistSearchForm
from app_notifications.utils import create_notification
from app_accounts.models import CustomUser
from app_accounts.sms import send_maintenance_status_sms
from django.utils import timezone

# Keep the function-based view for reference
# @login_required
# def create_post(request):
#     ...

@method_decorator(login_required, name='dispatch')
class CreateMaintenancePostView(FormView):
    template_name = 'app_posts/create_post.html'
    form_class = MaintenancePostForm
    success_url = reverse_lazy('user_dashboard')
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the user to the form for additional validation
        kwargs['user'] = self.request.user
        return kwargs
    
    def get_initial(self):
        initial = super().get_initial()
        # Set initial deadline to 7 days from now
        initial['deadline'] = timezone.now() + timezone.timedelta(days=7)
        return initial
    
    def form_valid(self, form):
        post = form.save(commit=False)
        post.created_by = self.request.user
        post.save()
        
        # Send SMS notification to the user
        if self.request.user.phone_number:
            try:
                from app_accounts.sms import send_maintenance_status_sms
                send_maintenance_status_sms(
                    phone_number=self.request.user.phone_number,
                    status="created",
                    post_title=post.title
                )
            except Exception as e:
                # Log the error but don't prevent post creation
                print(f"SMS notification failed: {str(e)}")
        
        # Notify all specialists in the same category about the new post
        specialists = CustomUser.objects.filter(
            user_type='specialist',
            category=post.category
        )
        
        for specialist in specialists:
            create_notification(
                recipient=specialist,
                notification_type='new_post',
                title='New Maintenance Request',
                message=f'A new maintenance request "{post.title}" has been posted in your category.',
                related_post=post
            )
        
        messages.success(self.request, 'Maintenance post created successfully!')
        return super().form_valid(form)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Create Maintenance Request'
        context['categories'] = dict(CustomUser.CATEGORY_CHOICES)
        context['experience_levels'] = dict(MaintenancePost.EXPERIENCE_CHOICES)
        return context

@login_required
def apply_for_post(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id)
    
    if request.user.user_type != 'specialist' or request.user.category != post.category:
        messages.error(request, 'You are not eligible to apply for this post.')
        return redirect('specialist_dashboard')
    
    # Check if the post is still open
    if post.status != 'open':
        messages.error(request, 'This post is no longer accepting applications.')
        return redirect('post_detail', post_id=post.id)
    
    # Check if the user has already applied
    if SpecialistApplication.objects.filter(post=post, specialist=request.user).exists():
        messages.warning(request, 'You have already applied for this post.')
        return redirect('post_detail', post_id=post.id)
    
    if request.method == 'POST':
        form = SpecialistApplicationForm(request.POST, request.FILES)
        if form.is_valid():
            application = form.save(commit=False)
            application.post = post
            application.specialist = request.user
            application.save()
            
            # Create notification for the post owner
            create_notification(
                recipient=post.created_by,
                notification_type='new_application',
                title='New Application Received',
                message=f'{request.user.username} has applied for your maintenance request "{post.title}".',
                related_post=post,
                related_application=application
            )
            
            messages.success(request, 'Your application has been submitted successfully.')
            return redirect('post_detail', post_id=post.id)
    else:
        form = SpecialistApplicationForm()
    
    context = {
        'post': post,
        'form': form,
    }
    return render(request, 'app_posts/apply_for_post.html', context)

@login_required
def post_detail(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id)

    # Check if the user is authorized to view this post
    if request.user != post.created_by and request.user.user_type != 'specialist':
        messages.error(request, 'You are not authorized to view this post.')
        return redirect('dashboard_redirect')

    # For specialists, check if the post matches their category
    if request.user.user_type == 'specialist' and request.user.category != post.category:
        messages.error(request, 'This post is not in your category of expertise.')
        return redirect('specialist_dashboard')

    # Mark related notifications as read when viewing the post
    from app_notifications.models import Notification
    unread_notifications = Notification.objects.filter(
        recipient=request.user,
        related_post=post,
        is_read=False
    )
    if unread_notifications.exists():
        unread_notifications.update(is_read=True)
    
    # Get applications for this post (for post owner)
    applications = None
    if request.user == post.created_by:
        applications = SpecialistApplication.objects.filter(post=post).select_related('specialist')
    
    # Check if the specialist has already applied
    has_applied = False
    user_application = None
    if request.user.user_type == 'specialist':
        user_application = SpecialistApplication.objects.filter(post=post, specialist=request.user).first()
        has_applied = user_application is not None
    
    if request.method == 'POST':
        form = MaintenancePostForm(request.POST, instance=post)
        if form.is_valid():
            updated_post = form.save()
            
            # Notify specialists if the post was updated
            specialists = CustomUser.objects.filter(
                user_type='specialist',
                category=updated_post.category
            )
            
            for specialist in specialists:
                # Check if specialist has already applied
                if not SpecialistApplication.objects.filter(post=post, specialist=specialist).exists():
                    create_notification(
                        recipient=specialist,
                        notification_type='post_updated',
                        title='Maintenance Request Updated',
                        message=f'The maintenance request "{updated_post.title}" has been updated.',
                        related_post=updated_post
                    )
            
            messages.success(request, 'Maintenance post updated successfully!')
            return redirect('post_detail', post_id=post.id)
    else:
        form = MaintenancePostForm(instance=post)
    
    # Get rating information for completed posts
    existing_rating = None
    can_rate_specialist = False
    specialist_ratings = []

    if post.status == 'completed':
        # Get all ratings for this post
        specialist_ratings = SpecialistRating.objects.filter(post=post).select_related('rated_by', 'specialist')

        # Check if the current user can rate the specialist
        if request.user == post.created_by and post.assigned_to:
            # Check if user has already rated this specialist for this post
            existing_rating = SpecialistRating.objects.filter(
                post=post,
                specialist=post.assigned_to,
                rated_by=request.user
            ).first()

            # User can rate if they haven't rated yet
            can_rate_specialist = existing_rating is None

    context = {
        'post': post,
        'form': form,
        'applications': applications,
        'has_applied': has_applied,
        'user_application': user_application,
        'existing_rating': existing_rating,
        'can_rate_specialist': can_rate_specialist,
        'specialist_ratings': specialist_ratings,
    }

    return render(request, 'app_posts/post_detail.html', context)

@login_required
def edit_post(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id)
    original_status = post.status
    
    # Check if the user is authorized to edit this post
    if request.user != post.created_by:
        messages.error(request, 'You are not authorized to edit this post.')
        return redirect('dashboard_redirect')
    
    # Only allow editing if the post is still open
    if post.status != 'open':
        messages.error(request, 'You can only edit posts that are still open.')
        return redirect('post_detail', post_id=post.id)
    
    if request.method == 'POST':
        form = MaintenancePostForm(request.POST, instance=post)
        if form.is_valid():
            updated_post = form.save()
            
            # Notify specialists if the post was updated
            specialists = CustomUser.objects.filter(
                user_type='specialist',
                category=updated_post.category
            )
            
            for specialist in specialists:
                # Check if specialist has already applied
                if not SpecialistApplication.objects.filter(post=post, specialist=specialist).exists():
                    create_notification(
                        recipient=specialist,
                        notification_type='post_updated',
                        title='Maintenance Request Updated',
                        message=f'The maintenance request "{updated_post.title}" has been updated.',
                        related_post=updated_post
                    )
            
            messages.success(request, 'Maintenance post updated successfully!')
            return redirect('post_detail', post_id=post.id)
    else:
        form = MaintenancePostForm(instance=post)
    
    return render(request, 'app_posts/edit_post.html', {'form': form, 'post': post})

@login_required
def close_post(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id, created_by=request.user)
    
    if post.status in ['completed', 'closed']:
        messages.error(request, 'This post is already closed or completed.')
        return redirect('post_detail', post_id=post.id)
    
    post.status = 'closed'
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            from app_accounts.sms import send_maintenance_status_sms
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="closed",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Notify specialists who applied for this post
    applicants = SpecialistApplication.objects.filter(post=post).select_related('specialist')
    for application in applicants:
        create_notification(
            recipient=application.specialist,
            notification_type='post_status',
            title='Maintenance Request Closed',
            message=f'The maintenance request "{post.title}" has been closed by the client.',
            related_post=post,
            related_application=application
        )
        
        # Send SMS to specialist
        if application.specialist.phone_number:
            try:
                from app_accounts.sms import send_specialist_status_sms
                send_specialist_status_sms(
                    phone_number=application.specialist.phone_number,
                    status="closed",
                    post_title=post.title
                )
            except Exception as e:
                print(f"SMS notification failed: {str(e)}")
    
    messages.success(request, 'Maintenance post closed successfully!')
    return redirect('post_detail', post_id=post.id)

@login_required
def accept_application(request, application_id):
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post
    
    # Check if the user is the post owner
    if request.user != post.created_by:
        messages.error(request, 'You are not authorized to accept applications for this post.')
        return redirect('dashboard_redirect')
    
    if post.status != 'open':
        messages.error(request, 'This post is no longer accepting applications.')
        return redirect('post_detail', post_id=post.id)
    
    # Update application status
    application.status = 'accepted'
    application.save()
    
    # Update post status
    post.status = 'pending_confirmation'
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            from app_accounts.sms import send_maintenance_status_sms
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="pending_confirmation",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Send SMS to specialist
    if application.specialist.phone_number:
        try:
            from app_accounts.sms import send_specialist_status_sms
            send_specialist_status_sms(
                phone_number=application.specialist.phone_number,
                status="pending_confirmation",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the specialist
    create_notification(
        recipient=application.specialist,
        notification_type='application_status',
        title='Application Accepted',
        message=f'Your application for "{post.title}" has been accepted. Please confirm to start the job.',
        related_post=post,
        related_application=application
    )
    
    # Notify other applicants that the post is no longer accepting applications
    other_applications = SpecialistApplication.objects.filter(
        post=post
    ).exclude(id=application.id)
    
    for other_app in other_applications:
        create_notification(
            recipient=other_app.specialist,
            notification_type='application_status',
            title='Application Status Update',
            message=f'The maintenance request "{post.title}" is no longer accepting applications.',
            related_post=post,
            related_application=other_app
        )
    
    messages.success(request, 'Application accepted. Waiting for specialist confirmation.')
    return redirect('post_detail', post_id=post.id)

@login_required
def confirm_application(request, application_id):
    application = get_object_or_404(SpecialistApplication, id=application_id, specialist=request.user)
    post = application.post
    
    if post.status != 'pending_confirmation' or application.status != 'accepted':
        messages.error(request, 'This application cannot be confirmed at this time.')
        return redirect('post_detail', post_id=post.id)
    
    # Update application status and post status
    application.status = 'confirmed'
    application.save()
    
    post.status = 'in_progress'
    post.assigned_to = request.user
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="in_progress",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the post owner
    create_notification(
        recipient=post.created_by,
        notification_type='post_status',
        title='Maintenance Started',
        message=f'The specialist has confirmed and started work on "{post.title}".',
        related_post=post,
        related_application=application
    )
    
    messages.success(request, 'Application confirmed. The job is now in progress.')
    return redirect('active_jobs')

@login_required
def decline_job(request, application_id):
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post
    
    # Check if the user is the assigned specialist
    if request.user != application.specialist:
        messages.error(request, 'You are not authorized to decline this job.')
        return redirect('dashboard_redirect')
    
    # Check if the application is in accepted status
    if application.status != 'accepted':
        messages.error(request, 'This application cannot be declined.')
        return redirect('post_detail', post_id=post.id)
    
    # Update application status
    application.status = 'declined'
    application.save()
    
    # Reset post status and assigned specialist
    post.status = 'open'
    post.assigned_to = None
    post.save()
    
    messages.success(request, 'You have declined this job. The post has been reopened for other specialists.')
    return redirect('specialist_dashboard')

@login_required
def reject_application(request, application_id):
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post
    
    # Check if the user is authorized to reject this application
    if request.user != post.created_by:
        messages.error(request, 'You are not authorized to reject applications for this post.')
        return redirect('dashboard_redirect')
    
    # Check if the post is still open
    if post.status != 'open':
        messages.error(request, 'This post is no longer accepting applications.')
        return redirect('post_detail', post_id=post.id)
    
    # Check if the application is still pending
    if application.status != 'pending':
        messages.error(request, 'This application has already been processed.')
        return redirect('post_detail', post_id=post.id)
    
    # Update application status
    application.status = 'rejected'
    application.save()
    
    messages.success(request, f'Application from {application.specialist.username} has been rejected.')
    return redirect('post_detail', post_id=post.id)

@login_required
def complete_job(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id, assigned_to=request.user)
    
    if post.status != 'in_progress':
        messages.error(request, 'This job cannot be marked as complete at this time.')
        return redirect('post_detail', post_id=post.id)
    
    # Update post status to pending approval
    post.status = 'pending_approval'
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            from app_accounts.sms import send_maintenance_status_sms
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="pending_approval",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Send SMS to specialist
    if request.user.phone_number:
        try:
            from app_accounts.sms import send_specialist_status_sms
            send_specialist_status_sms(
                phone_number=request.user.phone_number,
                status="pending_approval",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the post owner
    create_notification(
        recipient=post.created_by,
        notification_type='job_completed',
        title='Job Marked as Complete',
        message=f'The specialist has marked the maintenance job "{post.title}" as complete. Please review and approve.',
        related_post=post
    )
    
    messages.success(request, 'Job marked as complete. Waiting for client approval.')
    return redirect('post_detail', post_id=post.id)

@login_required
def approve_job(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id, created_by=request.user)
    
    if post.status != 'pending_approval':
        messages.error(request, 'This job cannot be approved at this time.')
        return redirect('post_detail', post_id=post.id)
    
    # Update post status to completed
    post.status = 'completed'
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            from app_accounts.sms import send_maintenance_status_sms
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="completed",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Send SMS to specialist
    if post.assigned_to and post.assigned_to.phone_number:
        try:
            from app_accounts.sms import send_specialist_status_sms
            send_specialist_status_sms(
                phone_number=post.assigned_to.phone_number,
                status="completed",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the specialist
    create_notification(
        recipient=post.assigned_to,
        notification_type='job_approved',
        title='Job Approved',
        message=f'The client has approved your work on "{post.title}". The job is now marked as completed.',
        related_post=post
    )
    
    messages.success(request, 'Job completion approved. The job is now marked as completed.')
    return redirect('post_detail', post_id=post.id)

@login_required
def reject_job(request, post_id):
    post = get_object_or_404(MaintenancePost, id=post_id, created_by=request.user)
    
    if post.status != 'pending_approval':
        messages.error(request, 'This job cannot be rejected at this time.')
        return redirect('post_detail', post_id=post.id)
    
    # Update post status back to in progress
    post.status = 'in_progress'
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            from app_accounts.sms import send_maintenance_status_sms
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="in_progress",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Send SMS to specialist
    if post.assigned_to and post.assigned_to.phone_number:
        try:
            from app_accounts.sms import send_specialist_status_sms
            send_specialist_status_sms(
                phone_number=post.assigned_to.phone_number,
                status="in_progress",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the specialist
    create_notification(
        recipient=post.assigned_to,
        notification_type='job_rejected',
        title='Additional Work Requested',
        message=f'The client has requested additional work on "{post.title}". The job has been moved back to in-progress status.',
        related_post=post
    )
    
    messages.success(request, 'Job completion rejected. The specialist has been notified to continue working on this job.')
    return redirect('post_detail', post_id=post.id)

@login_required
def rate_specialist(request, application_id):
    """View for rating a specialist after job completion"""
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post
    
    # Check if the user is authorized to rate this specialist
    if request.user != post.created_by:
        messages.error(request, 'You are not authorized to rate this specialist.')
        return redirect('dashboard_redirect')
    
    # Check if the post is completed
    if post.status != 'completed':
        messages.error(request, 'You can only rate specialists for completed maintenance requests.')
        return redirect('post_detail', post_id=post.id)
    
    # Check if the specialist has already been rated for this post
    try:
        existing_rating = SpecialistRating.objects.get(
            specialist=application.specialist,
            post=post
        )
        messages.info(request, 'You have already rated this specialist for this job.')
        return redirect('post_detail', post_id=post.id)
    except SpecialistRating.DoesNotExist:
        pass
    
    if request.method == 'POST':
        rating_value = int(request.POST.get('rating', 0))
        comment = request.POST.get('comment', '')
        
        if rating_value < 1 or rating_value > 5:
            messages.error(request, 'Please provide a valid rating between 1 and 5.')
        else:
            # Create the rating
            SpecialistRating.objects.create(
                specialist=application.specialist,
                post=post,
                rated_by=request.user,
                rating=rating_value,
                comment=comment
            )
            
            # Create notification for the specialist
            create_notification(
                recipient=application.specialist,
                notification_type='new_rating',
                title='New Rating Received',
                message=f'You received a {rating_value}/5 rating for "{post.title}".',
                related_post=post
            )
            
            messages.success(request, 'Thank you for rating the specialist!')
            return redirect('post_detail', post_id=post.id)
    
    context = {
        'application': application,
        'post': post,
        'specialist': application.specialist
    }
    
    return render(request, 'app_posts/rate_specialist.html', context)

@login_required
def application_detail(request, application_id):
    """View to display application details with specialist profile and ratings"""
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post
    specialist = application.specialist

    # Check if the user is authorized to view this application
    # Only the post owner or the specialist who applied can view the application details
    if request.user != post.created_by and request.user != specialist:
        messages.error(request, 'You are not authorized to view this application.')
        return redirect('dashboard_redirect')

    # Get specialist's ratings and statistics
    specialist_ratings = SpecialistRating.objects.filter(specialist=specialist).order_by('-created_at')
    average_rating = specialist.get_average_rating()
    total_ratings = specialist.get_total_ratings_count()
    completed_jobs = specialist.get_completed_jobs_count()

    # Get recent completed jobs for this specialist
    recent_jobs = SpecialistApplication.objects.filter(
        specialist=specialist,
        post__status='completed'
    ).select_related('post').order_by('-post__created_at')[:5]

    context = {
        'application': application,
        'post': post,
        'specialist': specialist,
        'specialist_ratings': specialist_ratings,
        'average_rating': average_rating,
        'total_ratings': total_ratings,
        'completed_jobs': completed_jobs,
        'recent_jobs': recent_jobs,
    }

    return render(request, 'app_posts/application_detail.html', context)

@login_required
def confirm_application_with_post(request, post_id, application_id):
    """
    Legacy URL support for confirming applications with post_id in the URL
    """
    application = get_object_or_404(SpecialistApplication, id=application_id)
    post = application.post

    # Check if the user is the specialist who applied
    if request.user != application.specialist:
        messages.error(request, 'You are not authorized to confirm this application.')
        return redirect('dashboard_redirect')

    if application.status != 'accepted':
        messages.error(request, 'This application cannot be confirmed at this time.')
        return redirect('post_detail', post_id=post.id)
    
    # Update application status
    application.status = 'confirmed'
    application.save()
    
    # Update post status
    post.status = 'in_progress'
    post.assigned_to = application.specialist
    post.save()
    
    # Send SMS to post owner
    if post.created_by.phone_number:
        try:
            send_maintenance_status_sms(
                phone_number=post.created_by.phone_number,
                status="in_progress",
                post_title=post.title
            )
        except Exception as e:
            print(f"SMS notification failed: {str(e)}")
    
    # Create notification for the post owner
    create_notification(
        recipient=post.created_by,
        notification_type='post_status',
        title='Maintenance Started',
        message=f'The specialist has confirmed and started work on "{post.title}".',
        related_post=post,
        related_application=application
    )
    
    messages.success(request, 'You have confirmed this maintenance job. Please contact the client to arrange details.')
    return redirect('post_detail', post_id=post.id)

@login_required
def search_posts(request):
    """Search and filter maintenance posts"""
    form = PostSearchForm(request.GET or None)

    # Start with base queryset - only show posts in categories that have specialists
    specialist_categories = CustomUser.objects.filter(
        user_type='specialist',
        category__isnull=False
    ).values_list('category', flat=True).distinct()

    posts = MaintenancePost.objects.filter(category__in=specialist_categories)

    if form.is_valid():
        # Search query
        search_query = form.cleaned_data.get('search')
        if search_query:
            posts = posts.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(location__icontains=search_query) |
                Q(organization_name__icontains=search_query)
            )

        # Category filter (now multiple choice)
        categories = form.cleaned_data.get('category')
        if categories:
            posts = posts.filter(category__in=categories)

        # Status filter
        status = form.cleaned_data.get('status')
        if status:
            posts = posts.filter(status=status)

        # Experience filter
        experience = form.cleaned_data.get('experience')
        if experience:
            posts = posts.filter(experience=experience)

        # Requester type filter
        requester_type = form.cleaned_data.get('requester_type')
        if requester_type:
            posts = posts.filter(requester_type=requester_type)

        # Date range filters
        date_from = form.cleaned_data.get('date_from')
        if date_from:
            posts = posts.filter(created_at__date__gte=date_from)

        date_to = form.cleaned_data.get('date_to')
        if date_to:
            posts = posts.filter(created_at__date__lte=date_to)

        # Sort
        sort_by = form.cleaned_data.get('sort_by') or '-created_at'
        posts = posts.order_by(sort_by)
    else:
        posts = posts.order_by('-created_at')

    # Additional filtering based on user type
    if request.user.user_type == 'specialist':
        # Specialists can see all posts in their category (including completed and closed)
        posts = posts.filter(category=request.user.category)
    elif request.user.user_type == 'user':
        # Regular users can see all posts or filter to their own
        show_own_only = request.GET.get('own_only')
        if show_own_only:
            posts = posts.filter(created_by=request.user)

    # Get the count before pagination
    total_results = posts.count()

    # Pagination - show 6 posts per page
    paginator = Paginator(posts, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get available categories for display
    available_categories = specialist_categories

    context = {
        'form': form,
        'posts': page_obj,
        'page_obj': page_obj,
        'total_results': total_results,
        'available_categories': available_categories,
    }

    return render(request, 'app_posts/search_posts.html', context)

@login_required
def search_specialists(request):
    """Search and filter specialists"""
    form = SpecialistSearchForm(request.GET or None)
    specialists = CustomUser.objects.filter(user_type='specialist')

    if form.is_valid():
        # Search query
        search_query = form.cleaned_data.get('search')
        if search_query:
            specialists = specialists.filter(
                Q(username__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(bio__icontains=search_query)
            )

        # Category filter
        category = form.cleaned_data.get('category')
        if category:
            specialists = specialists.filter(category=category)

        # Rating filter
        min_rating = form.cleaned_data.get('min_rating')
        if min_rating:
            # This will be implemented with a custom annotation
            pass  # We'll add this logic after creating the annotation

        # Sort
        sort_by = form.cleaned_data.get('sort_by') or '-date_joined'
        if sort_by in ['rating', 'completed_jobs']:
            # Custom sorting will be implemented with annotations
            specialists = specialists.order_by('-date_joined')
        else:
            specialists = specialists.order_by(sort_by)
    else:
        specialists = specialists.order_by('-date_joined')

    # Add rating and job count annotations
    specialists_with_stats = []
    for specialist in specialists:
        specialist.avg_rating = specialist.get_average_rating()
        specialist.total_ratings = specialist.get_total_ratings_count()
        specialist.completed_jobs_count = specialist.get_completed_jobs_count()
        specialists_with_stats.append(specialist)

    # Apply rating filter if specified
    if form.is_valid():
        min_rating = form.cleaned_data.get('min_rating')
        if min_rating:
            min_rating_value = float(min_rating)
            specialists_with_stats = [
                s for s in specialists_with_stats
                if s.avg_rating and s.avg_rating >= min_rating_value
            ]

    # Apply custom sorting
    if form.is_valid():
        sort_by = form.cleaned_data.get('sort_by')
        if sort_by == 'rating':
            specialists_with_stats.sort(key=lambda x: x.avg_rating or 0, reverse=True)
        elif sort_by == 'completed_jobs':
            specialists_with_stats.sort(key=lambda x: x.completed_jobs_count, reverse=True)

    # Pagination
    paginator = Paginator(specialists_with_stats, 6)  # Show 6 specialists per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'form': form,
        'specialists': page_obj,
        'page_obj': page_obj,
        'total_results': len(specialists_with_stats),
    }

    return render(request, 'app_posts/search_specialists.html', context)

