// Import Bootstrap functions, variables, and maps
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/variables-dark";

// Define custom colors
$custom-primary: #0d6efd;
$custom-secondary: #6c757d;
$custom-success: #198754;
$custom-info: #0dcaf0;
$custom-warning: #ffc107;
$custom-danger: #dc3545;
$custom-light: #f8f9fa;
$custom-dark: #212529;

// Create a custom color
$custom-colors: (
  "primary": $custom-primary,
  "secondary": $custom-secondary,
  "success": $custom-success,
  "info": $custom-info,
  "warning": $custom-warning,
  "danger": $custom-danger,
  "light": $custom-light,
  "dark": $custom-dark
);

// Merge with theme colors
$theme-colors: map-merge($theme-colors, $custom-colors);

// Import Bootstrap maps
@import "bootstrap/scss/maps";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/utilities";

// Light mode
$custom-colors-text: (
  "primary": $custom-primary,
  "success": $custom-success,
  "info": $custom-info,
  "warning": $custom-warning,
  "danger": $custom-danger
);

$custom-colors-bg-subtle: (
  "primary": tint-color($custom-primary, 80%),
  "success": tint-color($custom-success, 80%),
  "info": tint-color($custom-info, 80%),
  "warning": tint-color($custom-warning, 80%),
  "danger": tint-color($custom-danger, 80%)
);

$custom-colors-border-subtle: (
  "primary": tint-color($custom-primary, 60%),
  "success": tint-color($custom-success, 60%),
  "info": tint-color($custom-info, 60%),
  "warning": tint-color($custom-warning, 60%),
  "danger": tint-color($custom-danger, 60%)
);

$theme-colors-text: map-merge($theme-colors-text, $custom-colors-text);
$theme-colors-bg-subtle: map-merge($theme-colors-bg-subtle, $custom-colors-bg-subtle);
$theme-colors-border-subtle: map-merge($theme-colors-border-subtle, $custom-colors-border-subtle);

// Dark mode
$custom-colors-text-dark: (
  "primary": tint-color($custom-primary, 40%),
  "success": tint-color($custom-success, 40%),
  "info": tint-color($custom-info, 40%),
  "warning": tint-color($custom-warning, 40%),
  "danger": tint-color($custom-danger, 40%)
);

$custom-colors-bg-subtle-dark: (
  "primary": shade-color($custom-primary, 60%),
  "success": shade-color($custom-success, 60%),
  "info": shade-color($custom-info, 60%),
  "warning": shade-color($custom-warning, 60%),
  "danger": shade-color($custom-danger, 60%)
);

$custom-colors-border-subtle-dark: (
  "primary": shade-color($custom-primary, 40%),
  "success": shade-color($custom-success, 40%),
  "info": shade-color($custom-info, 40%),
  "warning": shade-color($custom-warning, 40%),
  "danger": shade-color($custom-danger, 40%)
);

$theme-colors-text-dark: map-merge($theme-colors-text-dark, $custom-colors-text-dark);
$theme-colors-bg-subtle-dark: map-merge($theme-colors-bg-subtle-dark, $custom-colors-bg-subtle-dark);
$theme-colors-border-subtle-dark: map-merge($theme-colors-border-subtle-dark, $custom-colors-border-subtle-dark);

// Import Bootstrap
@import "bootstrap/scss/root";
@import "bootstrap/scss/reboot";
@import "bootstrap/scss/type";
@import "bootstrap/scss/containers";
@import "bootstrap/scss/grid";
@import "bootstrap/scss/tables";
@import "bootstrap/scss/forms";
@import "bootstrap/scss/buttons";
@import "bootstrap/scss/transitions";
@import "bootstrap/scss/dropdown";
@import "bootstrap/scss/button-group";
@import "bootstrap/scss/nav";
@import "bootstrap/scss/navbar";
@import "bootstrap/scss/card";
@import "bootstrap/scss/accordion";
@import "bootstrap/scss/breadcrumb";
@import "bootstrap/scss/pagination";
@import "bootstrap/scss/badge";
@import "bootstrap/scss/alert";
@import "bootstrap/scss/progress";
@import "bootstrap/scss/list-group";
@import "bootstrap/scss/close";
@import "bootstrap/scss/toasts";
@import "bootstrap/scss/modal";
@import "bootstrap/scss/tooltip";
@import "bootstrap/scss/popover";
@import "bootstrap/scss/carousel";
@import "bootstrap/scss/spinners";
@import "bootstrap/scss/offcanvas";
@import "bootstrap/scss/placeholders";
@import "bootstrap/scss/helpers";
@import "bootstrap/scss/utilities/api";

// Custom styles
.dashboard-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  border: none;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

[data-bs-theme="dark"] .dashboard-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  }
}

// Status badges
.badge {
  &.bg-pending {
    background-color: $custom-warning;
    color: $custom-dark;
  }
  
  &.bg-assigned {
    background-color: $custom-info;
    color: $custom-dark;
  }
  
  &.bg-in-progress {
    background-color: $custom-primary;
    color: white;
  }
  
  &.bg-completed {
    background-color: $custom-success;
    color: white;
  }
  
  &.bg-cancelled {
    background-color: $custom-danger;
    color: white;
  }
}

// Quick action buttons
.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background-color: $custom-light;
  transition: all 0.3s ease;
  height: 100%;
  
  i {
    font-size: 2rem;
    margin-bottom: 15px;
  }
  
  span {
    font-weight: 500;
  }
  
  &:hover {
    background-color: $custom-primary;
    color: white;
    transform: translateY(-5px);
  }
}

[data-bs-theme="dark"] .quick-action {
  background-color: $custom-dark;
}