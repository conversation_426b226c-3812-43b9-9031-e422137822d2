from django.contrib import admin
from .models import SupportMessage
from django.utils import timezone
from smartit_maintenance.admin import admin_site

class SupportMessageAdmin(admin.ModelAdmin):
    list_display = ('subject', 'user', 'created_at', 'is_resolved', 'has_reply')
    list_filter = ('is_resolved', 'created_at')
    search_fields = ('subject', 'message', 'user__username', 'user__email')
    readonly_fields = ('user', 'created_at', 'replied_at')
    actions = ['mark_as_resolved', 'mark_as_unresolved']
    
    fieldsets = (
        ('Message Information', {
            'fields': ('subject', 'message', 'user', 'created_at')
        }),
        ('Admin Response', {
            'fields': ('admin_reply', 'replied_at', 'is_resolved')
        }),
    )
    
    def has_reply(self, obj):
        return bool(obj.admin_reply)
    has_reply.boolean = True
    has_reply.short_description = "Replied"
    
    def save_model(self, request, obj, form, change):
        if form.cleaned_data.get('admin_reply') and (not obj.admin_reply or obj.admin_reply != form.cleaned_data.get('admin_reply')):
            # This is a new reply or the reply has been updated
            obj.replied_at = timezone.now()
            obj.is_resolved = True
            
            # Save the object first
            super().save_model(request, obj, form, change)
            
            # Send email notification
            obj.send_reply_notification()
        else:
            super().save_model(request, obj, form, change)
    
    def mark_as_resolved(self, request, queryset):
        queryset.update(is_resolved=True)
    mark_as_resolved.short_description = "Mark selected messages as resolved"
    
    def mark_as_unresolved(self, request, queryset):
        queryset.update(is_resolved=False)
    mark_as_unresolved.short_description = "Mark selected messages as unresolved"
    
    class Media:
        css = {
            'all': ('css/admin_support.css',)
        }
        js = ('js/admin_support.js',)

# Register with the custom admin site
admin_site.register(SupportMessage, SupportMessageAdmin)

# Don't register with the default admin site to avoid duplication
# admin.site.register(SupportMessage, SupportMessageAdmin)



