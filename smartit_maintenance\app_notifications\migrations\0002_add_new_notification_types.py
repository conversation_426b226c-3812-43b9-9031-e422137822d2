# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_notifications', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[
                ('application_status', 'Application Status Change'),
                ('post_status', 'Post Status Change'),
                ('new_application', 'New Application'),
                ('job_completed', 'Job Completed'),
                ('job_approved', 'Job Approved'),
                ('job_rejected', 'Job Rejected'),
                ('new_post', 'New Maintenance Request'),
                ('post_updated', 'Maintenance Request Updated'),
                ('system', 'System Notification')
            ], max_length=20),
        ),
    ]