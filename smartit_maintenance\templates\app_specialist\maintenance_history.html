{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Maintenance History</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='maintenance_history' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Summary Stats -->
            {% if completed_jobs %}
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ total_completed }}</h3>
                            <p class="mb-0">Completed Jobs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ rated_jobs_count }}</h3>
                            <p class="mb-0">Jobs Rated</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0">
                                {% if average_rating > 0 %}
                                    {{ average_rating }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </h3>
                            <p class="mb-0">Avg Rating</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Completed Maintenance Jobs</h5>
                        {% if page_obj.paginator.count > 0 %}
                        <small class="text-muted">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} jobs
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if completed_jobs %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Post Title</th>
                                        <th>Client</th>
                                        <th>Location</th>
                                        <th>Completion Date</th>
                                        <th>Rating</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for job in completed_jobs %}
                                    <tr>
                                        <td>
                                            <strong>{{ job.post.title }}</strong>
                                            <br>
                                            <small class="text-muted">{{ job.post.get_category_display }}</small>
                                        </td>
                                        <td>
                                            {{ job.post.created_by.get_full_name|default:job.post.created_by.username }}
                                            <br>
                                            <small class="text-muted">{{ job.post.get_requester_type_display }}</small>
                                        </td>
                                        <td>
                                            {% if job.post.location %}
                                                {{ job.post.location }}
                                            {% else %}
                                                <span class="text-muted">Not specified</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ job.post.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            {% if job.rating %}
                                                <div class="d-flex align-items-center">
                                                    {% include 'includes/rating_stars.html' with rating=job.rating show_text=False size='sm' %}
                                                    <span class="ms-2">{{ job.rating }}/5</span>
                                                </div>
                                                {% if job.rating_comment %}
                                                    <small class="text-muted d-block mt-1">{{ job.rating_comment|truncatewords:10 }}</small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Not rated yet</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'post_detail' job.post.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Completed jobs pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Completed Jobs Yet</h5>
                            <p class="text-muted">You haven't completed any maintenance jobs yet. Once you complete jobs, they will appear here with client ratings and feedback.</p>
                            <a href="{% url 'specialist_dashboard' %}" class="btn btn-primary mt-3">
                                <i class="fas fa-search me-1"></i>Find Available Jobs
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


