{% extends "admin/index.html" %}
{% load i18n %}

{% block bodyclass %}{{ block.super }} app-{{ app_label }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo;
{% for app in app_list %}
{{ app.name }}
{% endfor %}
</div>
{% endblock %}

{% block content %}
<div class="app-{{ app_label }} module">
{% for app in app_list %}
    <table>
    <caption>
        <a href="{{ app.app_url }}" class="section" title="{% blocktranslate with name=app.name %}Models in the {{ name }} application{% endblocktranslate %}">{{ app.name }}</a>
    </caption>
    {% for model in app.models %}
        <tr class="model-{{ model.object_name|lower }}">
        {% if model.admin_url %}
            <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
        {% else %}
            <th scope="row">{{ model.name }}</th>
        {% endif %}

        {% if model.add_url %}
            <td><a href="{{ model.add_url }}" class="addlink">{% translate 'Add' %}</a></td>
        {% else %}
            <td>&nbsp;</td>
        {% endif %}
        </tr>
    {% endfor %}
    </table>
{% endfor %}
</div>
{% endblock %}


