{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Active Maintenance</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='active_maintenance' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4>Your Active Maintenance Tasks</h4>
                </div>
                <div class="card-body">
                    {% if active_posts %}
                        <div class="list-group">
                            {% for post in active_posts %}
                                <div class="list-group-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h5 class="mb-0">{{ post.title }}</h5>
                                        <span class="badge bg-{% if post.status == 'in_progress' %}primary{% elif post.status == 'pending_approval' %}warning{% endif %}">
                                            {% if post.status == 'in_progress' %}In Progress{% elif post.status == 'pending_approval' %}Pending Approval{% endif %}
                                        </span>
                                    </div>
                                    <p>{{ post.description|truncatewords:30 }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-info me-2">{{ post.get_category_display }}</span>
                                            <small class="text-muted">Started: {{ post.created_at|date:"M d, Y" }}</small>
                                        </div>
                                        <div>
                                            <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-primary">View Details</a>
                                            {% if post.status == 'pending_approval' %}
                                                <a href="{% url 'approve_job' post.id %}" class="btn btn-sm btn-success ms-1">Approve</a>
                                                <a href="{% url 'reject_job' post.id %}" class="btn btn-sm btn-danger ms-1">Request Changes</a>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% if post.assigned_to %}
                                        <div class="mt-2">
                                            <small class="text-muted">Assigned to: <strong>{{ post.assigned_to.username }}</strong></small>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>You don't have any active maintenance tasks at the moment.</p>
                            <a href="{% url 'create_post' %}" class="btn btn-primary mt-2">Create New Maintenance Request</a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Quick Tips Card -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4>Maintenance Tips</h4>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">Regularly check the status of your maintenance requests</li>
                        <li class="list-group-item">Communicate with specialists through the comment section</li>
                        <li class="list-group-item">Approve completed maintenance promptly to close the request</li>
                        <li class="list-group-item">If additional work is needed, request it through the approval section</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



