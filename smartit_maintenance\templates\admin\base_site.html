{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="{% static 'css/admin_custom.css' %}">
<style>
    /* Critical width fixes that need to be applied immediately */
    html, body {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }
    
    #container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        box-sizing: border-box;
    }
    
    #content {
        width: 100% !important;
        max-width: 100% !important;
        padding: 20px !important;
        box-sizing: border-box;
    }
    
    #content-main {
        width: 100% !important;
        float: none !important;
        box-sizing: border-box;
    }
    
    .colMS {
        margin: 0 !important;
        width: 100% !important;
        box-sizing: border-box;
    }
    
    .colM {
        width: 100% !important;
        box-sizing: border-box;
    }
    
    .module {
        width: 100% !important;
        box-sizing: border-box;
    }
    
    .admin-dashboard {
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0;
        box-sizing: border-box;
    }
</style>
{% endblock %}

{% block branding %}
<div id="branding">
    <h1 id="site-name">
        <a href="{% url 'admin:index' %}">
            <div class="admin-logo">
                <i class="fas fa-tools me-2"></i>
                <span>Smart-IT Admin</span>
            </div>
        </a>
    </h1>
</div>
{% endblock %}

{% block usertools %}
{% if has_permission %}
<div id="user-tools" class="d-flex align-items-center">
    <!-- Theme toggle button -->
    <div class="dropdown me-3">
        <button class="btn btn-sm dropdown-toggle" type="button" id="bd-theme" data-bs-toggle="dropdown" aria-expanded="false">
            <i id="theme-icon-admin" class="fas fa-moon"></i>
            <span class="d-none d-md-inline ms-1">Theme</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="bd-theme">
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
                    <i class="fas fa-sun me-2"></i>
                    Light
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
                    <i class="fas fa-moon me-2"></i>
                    Dark
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto" aria-pressed="false">
                    <i class="fas fa-circle-half-stroke me-2"></i>
                    Auto
                </button>
            </li>
        </ul>
    </div>
    
    <!-- User info -->
    <div class="user-info me-3">
        <span class="user-name">{{ request.user.username }}</span>
    </div>
    
    <!-- Logout form with POST method -->
    <form method="post" action="{% url 'admin_logout' %}" class="d-inline" id="logout-form">
        {% csrf_token %}
        <button type="submit" class="btn btn-outline-light btn-sm">
            <i class="fas fa-sign-out-alt me-1"></i> Logout
        </button>
    </form>
</div>
{% endif %}
{% endblock %}

{% block nav-global %}{% endblock %}

{% block footer %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        'use strict'

        const getStoredTheme = () => localStorage.getItem('theme')
        const setStoredTheme = theme => localStorage.setItem('theme', theme)

        const getPreferredTheme = () => {
            const storedTheme = getStoredTheme()
            if (storedTheme) {
                return storedTheme
            }

            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        }

        const setTheme = theme => {
            if (theme === 'auto') {
                document.documentElement.setAttribute('data-bs-theme', 
                    window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
                )
            } else {
                document.documentElement.setAttribute('data-bs-theme', theme)
            }
            
            // Update the icon
            updateThemeIcon(theme)
        }

        const updateThemeIcon = (theme) => {
            const themeIcon = document.getElementById('theme-icon-admin')
            if (themeIcon) {
                // Remove all icon classes
                themeIcon.classList.remove('fa-sun', 'fa-moon', 'fa-circle-half-stroke')
                
                // Add appropriate icon
                if (theme === 'dark' || 
                   (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    themeIcon.classList.add('fa-moon')
                } else if (theme === 'auto') {
                    themeIcon.classList.add('fa-circle-half-stroke')
                } else {
                    themeIcon.classList.add('fa-sun')
                }
            }
            
            // Update active state in dropdown
            document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
                element.classList.remove('active')
                element.setAttribute('aria-pressed', 'false')
                
                if (element.getAttribute('data-bs-theme-value') === theme) {
                    element.classList.add('active')
                    element.setAttribute('aria-pressed', 'true')
                }
            })
        }

        // Set initial theme
        setTheme(getPreferredTheme())

        // Add event listeners to theme buttons
        document.querySelectorAll('[data-bs-theme-value]').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const theme = toggle.getAttribute('data-bs-theme-value')
                setStoredTheme(theme)
                setTheme(theme)
            })
        })
        
        // Listen for OS theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
            const storedTheme = getStoredTheme()
            if (storedTheme === 'auto') {
                setTheme('auto')
            }
        })
    })
</script>
{% endblock %}






