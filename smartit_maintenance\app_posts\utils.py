from django.utils import timezone
from .models import MaintenancePost
from app_notifications.utils import create_notification
import logging

logger = logging.getLogger(__name__)

def close_expired_posts():
    """
    Close maintenance posts that have reached their deadline
    """
    # Get all open posts where deadline has passed
    expired_posts = MaintenancePost.objects.filter(
        status__in=['open', 'pending_confirmation', 'in_progress', 'pending_approval'],
        deadline__lt=timezone.now()
    )
    
    logger.info(f"Found {expired_posts.count()} expired posts to close")
    
    count = 0
    for post in expired_posts:
        # Update post status
        old_status = post.status
        post.status = 'closed'
        post.save()
        count += 1
        
        logger.info(f"Closed post ID {post.id}: '{post.title}' (was: {old_status})")
        
        # Send SMS to post owner
        if post.created_by.phone_number:
            try:
                from app_accounts.sms import send_maintenance_status_sms
                send_maintenance_status_sms(
                    phone_number=post.created_by.phone_number,
                    status="closed",
                    post_title=post.title
                )
            except Exception as e:
                logger.error(f"SMS notification failed for post {post.id}: {str(e)}")
        
        # Notify the post creator
        create_notification(
            recipient=post.created_by,
            notification_type='post_status',
            title='Maintenance Request Expired',
            message=f'Your maintenance request "{post.title}" has been automatically closed as it reached its deadline.',
            related_post=post
        )
        
        # Notify any specialists who applied for this post
        applications = post.applications.all()
        for application in applications:
            create_notification(
                recipient=application.specialist,
                notification_type='post_status',
                title='Maintenance Request Closed',
                message=f'The maintenance request "{post.title}" has been automatically closed as it reached its deadline.',
                related_post=post,
                related_application=application
            )
            
            # Send SMS to specialist
            if application.specialist.phone_number:
                try:
                    from app_accounts.sms import send_specialist_status_sms
                    send_specialist_status_sms(
                        phone_number=application.specialist.phone_number,
                        status="closed",
                        post_title=post.title
                    )
                except Exception as e:
                    logger.error(f"SMS notification failed for specialist {application.specialist.id}: {str(e)}")
    
    logger.info(f"Successfully closed {count} expired maintenance posts")
    return count


