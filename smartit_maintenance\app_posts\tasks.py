try:
    from celery import shared_task
    
    @shared_task
    def task_close_expired_posts():
        """
        Celery task to close expired maintenance posts
        """
        from .utils import close_expired_posts
        count = close_expired_posts()
        return f"Closed {count} expired maintenance posts"
        
except ImportError:
    # Define a regular function if Celery is not installed
    def task_close_expired_posts():
        """
        Regular function to close expired maintenance posts
        """
        from .utils import close_expired_posts
        count = close_expired_posts()
        return f"Closed {count} expired maintenance posts"

