{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Specialist Dashboard</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='dashboard' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h4>Available Maintenance Requests</h4>
                        </div>
                        <div class="card-body">
                            {% if posts %}
                                <div class="list-group">
                                    {% for post in posts %}
                                        <div class="list-group-item mb-2">
                                            <h5>{{ post.title }}</h5>
                                            <p>{{ post.description|truncatewords:20 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-info">{{ post.get_category_display }}</span>
                                                <span class="badge bg-secondary">Experience: {{ post.get_experience_display }}</span>
                                                <div>
                                                    <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-primary me-1">View Details</a>
                                                    <a href="{% url 'apply_for_post' post.id %}" class="btn btn-sm btn-primary">Apply</a>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p>No available maintenance requests in your category at the moment.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Application Stats Card -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h4>Application Stats</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6>Total Applications</h6>
                                <h3>{{ applications.count }}</h3>
                            </div>
                            <div class="mb-3">
                                <h6>Pending</h6>
                                <h3>{{ applications.pending_count|default:"0" }}</h3>
                            </div>
                            <div class="mb-3">
                                <h6>Accepted/Confirmed</h6>
                                <h3>{{ applications.accepted_count|default:"0" }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



