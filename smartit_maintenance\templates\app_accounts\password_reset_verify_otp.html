{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card mt-5">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Verify OTP</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> We've sent a 6-digit OTP to your phone. Please enter it below to continue.
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ form.otp_code.id_for_label }}" class="form-label">OTP Code</label>
                            {{ form.otp_code }}
                            <div class="form-text">Enter the 6-digit code sent to your phone</div>
                            {% if form.otp_code.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.otp_code.errors }}
                                </div>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">Verify OTP</button>
                    </form>
                    
                    <div class="mt-3">
                        <p>Didn't receive the code? <a href="{% url 'password_reset_request' %}">Request again</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}