from django.core.paginator import Paginator
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from app_posts.models import MaintenancePost

@login_required
def user_dashboard(request):
    # Get all posts for the current user
    posts_list = MaintenancePost.objects.filter(
        created_by=request.user
    ).order_by('-created_at')
    
    # Set up pagination
    paginator = Paginator(posts_list, 5)  # Show 5 posts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Count posts by status
    pending_count = MaintenancePost.objects.filter(
        created_by=request.user, 
        status='pending'
    ).count()
    
    in_progress_count = MaintenancePost.objects.filter(
        created_by=request.user, 
        status__in=['assigned', 'in_progress']
    ).count()
    
    completed_count = MaintenancePost.objects.filter(
        created_by=request.user, 
        status='completed'
    ).count()
    
    context = {
        'page_obj': page_obj,
        'pending_count': pending_count,
        'in_progress_count': in_progress_count,
        'completed_count': completed_count,
        'active_tab': 'dashboard'
    }
    
    return render(request, 'app_user/dashboard.html', context)

@login_required
def active_maintenance(request):
    # Get all active maintenance posts for the current user
    active_posts = MaintenancePost.objects.filter(
        created_by=request.user,
        status__in=['pending', 'assigned', 'in_progress']
    ).order_by('-created_at')
    
    return render(request, 'app_user/active_maintenance.html', {
        'active_posts': active_posts,
        'active_tab': 'active_maintenance'
    })

@login_required
def maintenance_history(request):
    # Get completed maintenance posts for the current user
    completed_posts = MaintenancePost.objects.filter(
        created_by=request.user,
        status='completed'
    ).order_by('-created_at')
    
    return render(request, 'app_user/maintenance_history.html', {
        'completed_posts': completed_posts,
        'active_tab': 'maintenance_history'
    })

@login_required
def user_notifications(request):
    # This view will be implemented when we add notifications functionality
    return render(request, 'app_user/notifications.html', {
        'active_tab': 'notifications'
    })

@login_required
def user_settings(request):
    # This view will be implemented when we add user settings functionality
    return render(request, 'app_user/settings.html', {
        'active_tab': 'settings'
    })

@login_required
def post_history(request):
    """View for users to see their post history with pagination"""
    posts_list = MaintenancePost.objects.filter(
        created_by=request.user
    ).order_by('-created_at')

    # Set up pagination - show 6 posts per page
    paginator = Paginator(posts_list, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'app_user/post_history.html', {
        'posts': page_obj,
        'page_obj': page_obj,
        'active_tab': 'post_history'
    })



