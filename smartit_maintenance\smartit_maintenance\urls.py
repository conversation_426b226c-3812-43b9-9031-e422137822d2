from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from app_accounts.views import admin_logout_view
from smartit_maintenance.admin import admin_site

urlpatterns = [
    path('admin/logout/', admin_logout_view, name='admin_logout'),  # Custom admin logout view
    path('admin/', admin_site.urls),  # Use the custom admin site
    path('', include('app_home.urls')),
    path('accounts/', include('app_accounts.urls')),
    path('user/', include('app_user.urls')),
    path('specialist/', include('app_specialist.urls')),
    path('posts/', include('app_posts.urls')),
    path('notifications/', include('app_notifications.urls')),
    path('reports/', include('app_reports.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

