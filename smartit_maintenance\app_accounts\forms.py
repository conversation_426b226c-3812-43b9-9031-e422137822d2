from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import CustomUser

class UserRegistrationForm(UserCreationForm):
    # Define limited user type choices for registration (excluding admin)
    USER_TYPE_CHOICES = (
        ('user', 'User'),
        ('specialist', 'Specialist'),
    )
    
    # Override the user_type field to use limited choices
    user_type = forms.ChoiceField(
        choices=USER_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Make phone number required
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        help_text="Enter your phone number with Tanzania country code (must be exactly 13 characters, e.g., +255757577419). Do not start with 0.",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+255757577419'})
    )
    
    # Add widgets to make the form look better
    username = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Username'})
    )
    
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email'})
    )
    
    password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Password'})
    )
    
    password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Confirm Password'})
    )
    
    # Get category choices from the model
    CATEGORY_CHOICES = (
        ('', 'Select Category'),
        ('networking', 'Networking'),
        ('hardware', 'Hardware'),
        ('software', 'Software'),
        ('database', 'Database'),
        ('security', 'Security'),
        ('other', 'Other'),
    )
    
    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=False,  # We'll validate this in clean()
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    class Meta:
        model = CustomUser
        fields = ['username', 'email', 'password1', 'password2', 'user_type', 'category', 'phone_number']
    
    def clean_username(self):
        username = self.cleaned_data.get('username')
        if username:
            # Check if username already exists
            if CustomUser.objects.filter(username=username).exists():
                raise forms.ValidationError("Username already used. Please choose a different username.")
        return username

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if phone_number:
            # Check if phone number starts with 0 (not allowed)
            if phone_number.startswith('0'):
                raise forms.ValidationError("Phone number cannot start with 0. Please use the international format starting with +255.")

            # Check phone number format and length
            if not phone_number.startswith('+255'):
                raise forms.ValidationError("Phone number must start with +255 (Tanzania country code).")

            # Check if phone number has exactly 13 characters (+255 + 9 digits)
            if len(phone_number) != 13:
                raise forms.ValidationError("Phone number must be exactly 13 characters long (e.g., +255757577419).")

            # Check if the part after +255 contains only digits
            phone_digits = phone_number[4:]  # Remove +255 prefix
            if not phone_digits.isdigit():
                raise forms.ValidationError("Phone number must contain only digits after +255.")

            # Check if phone number already exists
            if CustomUser.objects.filter(phone_number=phone_number).exists():
                raise forms.ValidationError("This phone number is already registered. Please use a different phone number.")
        return phone_number

    def clean(self):
        cleaned_data = super().clean()
        user_type = cleaned_data.get('user_type')
        category = cleaned_data.get('category')

        # Category is required ONLY for specialists
        if user_type == 'specialist' and (not category or category == ''):
            self.add_error('category', "Category is required for specialists")

        # For regular users, always set category to None
        if user_type == 'user':
            cleaned_data['category'] = None

        return cleaned_data

class ProfilePictureForm(forms.Form):
    profile_picture = forms.ImageField(
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        })
    )

class ProfileUpdateForm(forms.ModelForm):
    class Meta:
        model = CustomUser
        fields = ['username', 'first_name', 'last_name', 'email', 'phone_number', 'address', 'category', 'bio']
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4}),
            'address': forms.Textarea(attrs={'rows': 3}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'
            
        # Make category field optional for non-specialists
        if self.instance and self.instance.user_type != 'specialist':
            self.fields['category'].required = False
            self.fields['category'].widget = forms.HiddenInput()
            
            # Hide bio field for non-specialists
            if 'bio' in self.fields:
                self.fields['bio'].widget = forms.HiddenInput()






