from django.core.management.base import BaseCommand
import subprocess
import sys
import os

class Command(BaseCommand):
    help = 'Start Celery worker and beat for automatic post expiration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--worker-only',
            action='store_true',
            help='Start only the worker, not the beat scheduler',
        )
        parser.add_argument(
            '--beat-only',
            action='store_true',
            help='Start only the beat scheduler, not the worker',
        )

    def handle(self, *args, **options):
        if options['worker_only']:
            self.start_worker()
        elif options['beat_only']:
            self.start_beat()
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    'Starting both Celery worker and beat scheduler...\n'
                    'Run in separate terminals:\n'
                    '1. python manage.py start_celery --worker-only\n'
                    '2. python manage.py start_celery --beat-only'
                )
            )
            self.stdout.write(
                self.style.WARNING(
                    'Note: Make sure <PERSON><PERSON> is running before starting Celery'
                )
            )

    def start_worker(self):
        self.stdout.write(self.style.SUCCESS('Starting Celery worker...'))
        try:
            subprocess.run([
                sys.executable, '-m', 'celery', '-A', 'smartit_maintenance', 'worker', '--loglevel=info'
            ], cwd=os.getcwd())
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('Celery worker stopped'))

    def start_beat(self):
        self.stdout.write(self.style.SUCCESS('Starting Celery beat scheduler...'))
        try:
            subprocess.run([
                sys.executable, '-m', 'celery', '-A', 'smartit_maintenance', 'beat', '--loglevel=info'
            ], cwd=os.getcwd())
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('Celery beat stopped'))
