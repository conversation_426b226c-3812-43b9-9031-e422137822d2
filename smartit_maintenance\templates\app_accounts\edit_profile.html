{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Profile Update Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Edit Profile</h3>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <input type="hidden" name="form_type" value="profile_update">
                        
                        <!-- Account Information -->
                        <h5 class="border-bottom pb-2 mb-3">Account Information</h5>
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger">
                                    {{ form.username.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Personal Information -->
                        <h5 class="border-bottom pb-2 mb-3 mt-4">Personal Information</h5>
                        <!-- First and Last Name fields -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {{ form.first_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {{ form.last_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Contact Information -->
                        <h5 class="border-bottom pb-2 mb-3 mt-4">Contact Information</h5>
                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger">
                                    {{ form.email.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger">
                                    {{ form.phone_number.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">Enter your phone number with country code (e.g., +1234567890)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger">
                                    {{ form.address.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Specialist-specific fields -->
                        {% if user.user_type == 'specialist' %}
                        <h5 class="border-bottom pb-2 mb-3 mt-4">Professional Information</h5>
                        <div class="mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Specialization Category</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger">
                                    {{ form.category.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.bio.id_for_label }}" class="form-label">Bio/About Me</label>
                            {{ form.bio }}
                            {% if form.bio.errors %}
                                <div class="text-danger">
                                    {{ form.bio.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">Tell clients about your experience and expertise</div>
                        </div>
                        {% endif %}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'profile' %}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Password Change Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Change Password</h3>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <input type="hidden" name="form_type" value="password_change">
                        
                        {% for field in password_form %}
                        <div class="mb-3">
                            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                            {{ field }}
                            {% if field.errors %}
                                <div class="text-danger">
                                    {{ field.errors }}
                                </div>
                            {% endif %}
                            {% if field.help_text %}
                                <div class="form-text">{{ field.help_text }}</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
