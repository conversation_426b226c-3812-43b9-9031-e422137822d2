/* Custom styles for Smart-IT Maintenance */

/* Theme-specific variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --logo-bg: #fff;
    --logo-color: #0d6efd;
    --card-hover-transform: translateY(-5px);
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --card-hover-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

[data-bs-theme="dark"] {
    --primary-color: #4dabf7;
    --secondary-color: #adb5bd;
    --success-color: #51cf66;
    --info-color: #74c0fc;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --light-color: #343a40;
    --dark-color: #212529;
    --logo-bg: #212529;
    --logo-color: #4dabf7;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    --card-hover-shadow: 0 8px 16px rgba(0, 0, 0, 0.6);

    /* Dark mode specific colors */
    --bs-body-bg: #121212;
    --bs-body-color: #e9ecef;
    --bs-emphasis-color: #ffffff;
    --bs-secondary-color: #adb5bd;
    --bs-tertiary-color: #6c757d;
    --bs-border-color: #495057;
    --bs-secondary-bg: #1e1e1e;
    --bs-tertiary-bg: #2d3748;
}

/* Header and Navbar styling */
.navbar {
    padding: 0.8rem 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.navbar-dark.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%) !important;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    padding: 0;
}

/* Logo styling */
.logo {
    display: flex;
    align-items: center;
    margin-right: 5px;
}

.logo-icon {
    background-color: var(--logo-bg);
    color: var(--logo-color);
    width: 38px;
    height: 38px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.logo-text {
    font-weight: 700;
    font-size: 18px;
    color: #fff;
    margin-left: 10px;
    letter-spacing: 0.5px;
}

.brand-text {
    font-weight: 400;
    opacity: 0.9;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    padding-left: 10px;
    margin-left: 8px;
    letter-spacing: 0.5px;
}

.navbar-brand:hover .logo-icon {
    transform: rotate(15deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Navigation links */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: 0.8rem 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

.navbar-dark .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #ffffff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-dark .navbar-nav .nav-link:hover::after {
    width: 70%;
}

/* Dropdown styling */
.dropdown-menu {
    border: none;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    animation: fadeIn 0.3s ease;
}

.dropdown-item {
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    margin-right: 8px;
    color: #0d6efd;
}

.dropdown-divider {
    margin: 0.3rem 0;
}

/* Animation for dropdown */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Theme switcher styling */
.dropdown-item[data-bs-theme-value] {
    cursor: pointer;
}

.dropdown-item[data-bs-theme-value].active {
    background-color: var(--bs-primary);
    color: white;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: var(--primary-color);
        padding: 1rem;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-dark .navbar-nav .nav-link::after {
        display: none;
    }
    
    .navbar-dark .navbar-nav .nav-link {
        padding: 0.5rem 0;
    }
    
    .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        box-shadow: none;
    }
    
    .dropdown-item {
        color: rgba(255, 255, 255, 0.85);
    }
    
    .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
    }
    
    .dropdown-item i {
        color: #ffffff;
    }
    
    .dropdown-divider {
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* Footer styling - ensure visibility */
.footer {
    background-color: #000000 !important;
    color: #ffffff !important;
    width: 100% !important;
    position: relative !important;
    bottom: 0 !important;
    z-index: 100 !important;
    margin-top: auto !important;
    border-top: 4px solid #0d6efd !important;
    padding: 2rem 0 0 !important;
    display: block !important;
    visibility: visible !important;
}

.footer a {
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer a:hover {
    color: #0d6efd;
    text-decoration: none;
    transform: translateX(5px);
}

.footer-bottom {
    background-color: #111111;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    margin-top: 2rem;
}

.social-icons a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #333333;
    transition: all 0.3s ease;
    margin: 0 8px;
}

.social-icons a:hover {
    background-color: #0d6efd;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.footer h5 {
    color: #0d6efd;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    position: relative;
    font-size: 1.25rem;
}

.footer h5:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 3px;
    background: #0d6efd;
}

/* Newsletter form in footer */
.footer .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: #fff;
    border-radius: 20px;
    padding: 10px 15px;
}

.footer .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.footer .form-control:focus {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: none;
    border-color: #3498db;
}

.footer .btn-outline-light {
    border-radius: 20px;
    padding: 8px 20px;
    border: 2px solid #3498db;
    color: #3498db;
    background: transparent;
    transition: all 0.3s ease;
}

.footer .btn-outline-light:hover {
    background-color: #3498db;
    border-color: #3498db;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

/* Footer responsive adjustments */
@media (max-width: 767.98px) {
    .footer h5 {
        margin-top: 1.5rem;
    }
    
    .footer .col-md-6:first-child h5 {
        margin-top: 0;
    }
}

/* Footer animation */
@keyframes footerHighlight {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.footer-highlight {
    background: linear-gradient(-45deg, #2c3e50, #3498db, #2980b9, #1a252f);
    background-size: 400% 400%;
    animation: footerHighlight 15s ease infinite;
    border-top: 4px solid #3498db;
}

/* Make sure the body and html take full height */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Update main content area to ensure footer stays at bottom */
.main-content {
    flex: 1 0 auto;
    padding-bottom: 2rem;
}

/* Card styling with theme support */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: var(--card-shadow);
    border: none;
}

.card:hover {
    transform: var(--card-hover-transform);
    box-shadow: var(--card-hover-shadow);
}

/* Button hover effects */
.btn {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Form styling */
.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Notification styling */
.list-group-item-warning {
    border-left: 4px solid #ffc107;
}

.notification-badge {
    font-size: 0.8rem;
}

.notification-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Notification bell icon styling */
.nav-link .fas.fa-bell {
    position: relative;
    font-size: 1.1rem;
}

.nav-link .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Animation for new notifications */
@keyframes highlight {
    0% { background-color: rgba(255, 193, 7, 0.3); }
    100% { background-color: transparent; }
}

.new-notification {
    animation: highlight 2s ease-in-out;
}

/* Pulse animation for notification badge */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.nav-link .badge.bg-danger {
    animation: pulse 2s infinite;
}

/* Notification dropdown item styling */
.dropdown-menu .dropdown-item.small {
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 2px 4px;
}

.dropdown-menu .dropdown-item.small:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dropdown-menu .dropdown-item.small.bg-light {
    background-color: #fff3cd !important;
    border-left: 3px solid #ffc107;
}

.dropdown-menu .dropdown-item.small.bg-light:hover {
    background-color: #ffeaa7 !important;
}

/* Maintenance Post Form Styling */
.form-label {
    font-weight: 500;
}

.card-header {
    font-weight: 500;
}

.form-text {
    font-size: 0.85rem;
    color: #6c757d;
}

.needs-validation .form-control:focus,
.needs-validation .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.needs-validation .form-control.is-invalid:focus,
.needs-validation .form-select.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.needs-validation .form-control.is-valid:focus,
.needs-validation .form-select.is-valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Dashboard cards */
.dashboard-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: none;
}

.dashboard-card:hover {
    transform: var(--card-hover-transform);
    box-shadow: var(--card-hover-shadow);
}

/* Status badges */
.badge.bg-pending {
    background-color: var(--warning-color);
    color: #212529;
}

.badge.bg-assigned {
    background-color: var(--info-color);
    color: #212529;
}

.badge.bg-in-progress {
    background-color: var(--primary-color);
    color: white;
}

.badge.bg-completed {
    background-color: var(--success-color);
    color: white;
}

.badge.bg-cancelled {
    background-color: var(--danger-color);
    color: white;
}

/* Stat cards */
.stat-card {
    text-align: center;
    padding: 25px 15px;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1rem;
    color: var(--secondary-color);
}

/* Quick action buttons */
.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    background-color: var(--light-color);
    transition: all 0.3s ease;
    height: 100%;
}

[data-bs-theme="dark"] .quick-action {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-body-color);
    border: 1px solid var(--bs-border-color);
}

/* Dark mode enhancements */
[data-bs-theme="dark"] {
    background-color: var(--bs-body-bg) !important;
    color: var(--bs-body-color) !important;
}

/* Cards in dark mode */
[data-bs-theme="dark"] .card {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .card-header {
    background-color: var(--bs-tertiary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-emphasis-color);
}

/* Dropdowns in dark mode */
[data-bs-theme="dark"] .dropdown-menu {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .dropdown-item {
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .dropdown-item:hover,
[data-bs-theme="dark"] .dropdown-item:focus {
    background-color: var(--bs-tertiary-bg);
    color: var(--bs-emphasis-color);
}

[data-bs-theme="dark"] .dropdown-header {
    color: var(--bs-secondary-color);
}

[data-bs-theme="dark"] .dropdown-divider {
    border-color: var(--bs-border-color);
}

/* Tables in dark mode */
[data-bs-theme="dark"] .table {
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-bs-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--bs-tertiary-bg);
}

[data-bs-theme="dark"] .table-hover > tbody > tr:hover > td,
[data-bs-theme="dark"] .table-hover > tbody > tr:hover > th {
    background-color: var(--bs-secondary-bg);
}

/* Forms in dark mode */
[data-bs-theme="dark"] .form-control {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: var(--bs-secondary-bg);
    border-color: var(--primary-color);
    color: var(--bs-body-color);
    box-shadow: 0 0 0 0.25rem rgba(77, 171, 247, 0.25);
}

[data-bs-theme="dark"] .form-select {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .form-label {
    color: var(--bs-emphasis-color);
}

/* Alerts in dark mode */
[data-bs-theme="dark"] .alert-primary {
    background-color: rgba(77, 171, 247, 0.1);
    border-color: rgba(77, 171, 247, 0.2);
    color: var(--primary-color);
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(81, 207, 102, 0.1);
    border-color: rgba(81, 207, 102, 0.2);
    color: var(--success-color);
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(255, 212, 59, 0.1);
    border-color: rgba(255, 212, 59, 0.2);
    color: var(--warning-color);
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.2);
    color: var(--danger-color);
}

[data-bs-theme="dark"] .alert-info {
    background-color: rgba(116, 192, 252, 0.1);
    border-color: rgba(116, 192, 252, 0.2);
    color: var(--info-color);
}

/* Badges in dark mode */
[data-bs-theme="dark"] .badge {
    color: #000 !important;
}

[data-bs-theme="dark"] .badge.bg-primary {
    background-color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .badge.bg-success {
    background-color: var(--success-color) !important;
}

[data-bs-theme="dark"] .badge.bg-warning {
    background-color: var(--warning-color) !important;
}

[data-bs-theme="dark"] .badge.bg-danger {
    background-color: var(--danger-color) !important;
}

[data-bs-theme="dark"] .badge.bg-info {
    background-color: var(--info-color) !important;
}

/* Text colors for dark mode */
[data-bs-theme="dark"] .text-dark {
    color: var(--bs-emphasis-color) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: var(--bs-secondary-color) !important;
}

[data-bs-theme="dark"] .text-body {
    color: var(--bs-body-color) !important;
}

[data-bs-theme="dark"] .text-body-emphasis {
    color: var(--bs-emphasis-color) !important;
}

/* Background colors for dark mode */
[data-bs-theme="dark"] .bg-light {
    background-color: var(--bs-secondary-bg) !important;
    color: var(--bs-body-color) !important;
}

[data-bs-theme="dark"] .bg-white {
    background-color: var(--bs-secondary-bg) !important;
    color: var(--bs-body-color) !important;
}

/* Navbar dark mode enhancements */
[data-bs-theme="dark"] .navbar-dark {
    background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%) !important;
}

[data-bs-theme="dark"] .navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
}

[data-bs-theme="dark"] .navbar-dark .navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Pagination in dark mode */
[data-bs-theme="dark"] .page-link {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .page-link:hover {
    background-color: var(--bs-tertiary-bg);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-bs-theme="dark"] .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #000;
}

/* Modal in dark mode */
[data-bs-theme="dark"] .modal-content {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .modal-header {
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .modal-footer {
    border-color: var(--bs-border-color);
}

/* List group in dark mode */
[data-bs-theme="dark"] .list-group-item {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .list-group-item:hover {
    background-color: var(--bs-tertiary-bg);
}

[data-bs-theme="dark"] .list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #000;
}

.quick-action:hover {
    background-color: var(--primary-color);
    color: white;
    transform: var(--card-hover-transform);
}

.quick-action i {
    font-size: 2rem;
    margin-bottom: 15px;
}

.quick-action span {
    font-weight: 500;
}

