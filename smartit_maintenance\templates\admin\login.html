{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/login.css' %}">
<style>
    body {
        background-color: #f8f9fa;
        font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
    }
    
    .login-card {
        width: 100%;
        max-width: 450px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .login-header {
        background: linear-gradient(135deg, #0062cc 0%, #0097ff 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .login-logo {
        font-size: 3.5rem;
        margin-bottom: 15px;
    }
    
    .login-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0;
    }
    
    .login-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-top: 5px;
    }
    
    .login-body {
        padding: 30px;
    }
    
    .form-row {
        margin-bottom: 25px;
    }
    
    .form-row label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
    }
    
    .form-row input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 1rem;
        transition: border-color 0.2s;
    }
    
    .form-row input:focus {
        border-color: #0097ff;
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 151, 255, 0.2);
    }
    
    .submit-row {
        margin-top: 30px;
    }
    
    .submit-button {
        width: 100%;
        background-color: #0062cc;
        color: white;
        border: none;
        padding: 14px;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .submit-button:hover {
        background-color: #0056b3;
    }
    
    .back-link {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: #6c757d;
        text-decoration: none;
        font-size: 0.9rem;
    }
    
    .back-link:hover {
        color: #0062cc;
    }
    
    .errornote {
        color: #e74c3c;
        background-color: #fdecea;
        padding: 12px 15px;
        border-radius: 6px;
        margin-bottom: 20px;
        font-size: 0.9rem;
    }
    
    @media (max-width: 576px) {
        .login-header {
            padding: 20px;
        }
        
        .login-body {
            padding: 20px;
        }
    }
</style>
{{ form.media }}
{% endblock %}

{% block bodyclass %}{{ block.super }} login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-tools"></i>
            </div>
            <h1 class="login-title">Smart-IT Maintenance</h1>
            <p class="login-subtitle">Admin Portal</p>
        </div>
        
        <div class="login-body">
            {% if form.errors and not form.non_field_errors %}
            <p class="errornote">
                {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
            </p>
            {% endif %}

            {% if form.non_field_errors %}
            {% for error in form.non_field_errors %}
            <p class="errornote">
                {{ error }}
            </p>
            {% endfor %}
            {% endif %}

            <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
                <div class="form-row">
                    {{ form.username.errors }}
                    <label for="{{ form.username.id_for_label }}">
                        <i class="fas fa-user me-2"></i>{{ form.username.label }}
                    </label>
                    {{ form.username }}
                </div>
                <div class="form-row">
                    {{ form.password.errors }}
                    <label for="{{ form.password.id_for_label }}">
                        <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                    </label>
                    {{ form.password }}
                </div>
                
                <div class="submit-row">
                    <button type="submit" class="submit-button">
                        <i class="fas fa-sign-in-alt me-2"></i>{% translate 'Log in' %}
                    </button>
                </div>
            </form>
            
            <a href="{% url 'home' %}" class="back-link">
                <i class="fas fa-arrow-left me-1"></i> Back to main site
            </a>
        </div>
    </div>
</div>
{% endblock %}

