{% comment %}
Reusable rating stars component
Parameters:
- rating: The rating value (1-5)
- show_text: Whether to show rating text (default: false)
- size: Size of stars - 'sm', 'md', 'lg' (default: 'md')
- readonly: Whether stars are readonly (default: true)
{% endcomment %}

{% load static %}

<div class="rating-stars {% if size == 'sm' %}rating-sm{% elif size == 'lg' %}rating-lg{% endif %}">
    {% for i in "12345" %}
        {% if rating and forloop.counter <= rating %}
            <i class="fas fa-star text-warning"></i>
        {% else %}
            <i class="far fa-star text-muted"></i>
        {% endif %}
    {% endfor %}

    {% if show_text %}
        <span class="ms-2 text-muted">
            {% if rating %}
                {{ rating }}/5
            {% else %}
                No ratings yet
            {% endif %}
        </span>
    {% endif %}
</div>

<style>
.rating-stars {
    display: inline-flex;
    align-items: center;
}

.rating-stars i {
    margin-right: 2px;
}

.rating-sm i {
    font-size: 0.8rem;
}

.rating-lg i {
    font-size: 1.2rem;
}

.rating-stars .text-warning {
    color: #ffc107 !important;
}

.rating-stars .text-muted {
    color: #6c757d !important;
}
</style>
