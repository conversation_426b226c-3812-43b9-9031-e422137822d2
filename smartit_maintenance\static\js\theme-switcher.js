/*!
 * Theme switcher for Smart-IT Maintenance
 * Based on Bootstrap's theme switcher
 */

(() => {
  'use strict'

  const getStoredTheme = () => localStorage.getItem('theme')
  const setStoredTheme = theme => localStorage.setItem('theme', theme)

  const getPreferredTheme = () => {
    const storedTheme = getStoredTheme()
    if (storedTheme) {
      return storedTheme
    }

    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  const setTheme = theme => {
    if (theme === 'auto') {
      document.documentElement.setAttribute('data-bs-theme', 
        window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      )
    } else {
      document.documentElement.setAttribute('data-bs-theme', theme)
    }
    
    // Update the icon
    updateIcon(theme);
  }
  
  const updateIcon = theme => {
    const themeIcon = document.getElementById('theme-icon-admin') || document.getElementById('theme-icon');
    if (!themeIcon) return;
    
    // Remove existing classes
    themeIcon.classList.remove('fa-sun', 'fa-moon', 'fa-circle-half-stroke');
    
    // Add appropriate icon
    if (theme === 'dark' || (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      themeIcon.classList.add('fa-moon');
    } else if (theme === 'auto') {
      themeIcon.classList.add('fa-circle-half-stroke');
    } else {
      themeIcon.classList.add('fa-sun');
    }
  }

  const showActiveTheme = (theme) => {
    const btnToActive = document.querySelector(`[data-bs-theme-value="${theme}"]`)
    if (!btnToActive) return;
    
    document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
      element.classList.remove('active')
      element.setAttribute('aria-pressed', 'false')
    })

    btnToActive.classList.add('active')
    btnToActive.setAttribute('aria-pressed', 'true')
  }

  // Set initial theme
  setTheme(getPreferredTheme())

  // Update theme when system preference changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    const storedTheme = getStoredTheme()
    if (storedTheme !== 'light' && storedTheme !== 'dark') {
      setTheme(getPreferredTheme())
    }
  })

  // Set up event listeners when DOM is loaded
  window.addEventListener('DOMContentLoaded', () => {
    showActiveTheme(getPreferredTheme())

    document.querySelectorAll('[data-bs-theme-value]')
      .forEach(toggle => {
        toggle.addEventListener('click', () => {
          const theme = toggle.getAttribute('data-bs-theme-value')
          setStoredTheme(theme)
          setTheme(theme)
          showActiveTheme(theme)
        })
      })
  })
})()
