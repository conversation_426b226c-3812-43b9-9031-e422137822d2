from django.contrib import admin
from .models import MaintenancePost, SpecialistApplication
from django.utils.html import format_html
from smartit_maintenance.admin import admin_site

class SpecialistApplicationInline(admin.TabularInline):
    model = SpecialistApplication
    extra = 0
    readonly_fields = ('specialist', 'application_date', 'status')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False

class MaintenancePostAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'status', 'created_by', 'created_at', 'view_post')
    list_filter = ('status', 'category', 'created_at')
    search_fields = ('title', 'description', 'created_by__username')
    readonly_fields = ('created_by', 'created_at')
    date_hierarchy = 'created_at'
    inlines = [SpecialistApplicationInline]
    
    fieldsets = (
        ('Post Information', {
            'fields': ('title', 'description', 'category', 'status', 'location')
        }),
        ('Dates', {
            'fields': ('created_at',),
            'classes': ('collapse',),
        }),
        ('User Information', {
            'fields': ('created_by',),
            'classes': ('collapse',),
        }),
    )
    
    def view_post(self, obj):
        url = f"/posts/{obj.id}/"
        return format_html('<a href="{}">View Post</a>', url)
    view_post.short_description = "View"
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Register with the custom admin site
admin_site.register(MaintenancePost, MaintenancePostAdmin)

# Don't register with the default admin site to avoid duplication
# admin.site.register(MaintenancePost, MaintenancePostAdmin)

@admin.register(SpecialistApplication)
class SpecialistApplicationAdmin(admin.ModelAdmin):
    list_display = ('post', 'specialist', 'status', 'application_date')
    list_filter = ('status', 'application_date')
    search_fields = ('post__title', 'specialist__username', 'message')
    readonly_fields = ('application_date',)
    
    fieldsets = (
        ('Application Information', {
            'fields': ('post', 'specialist', 'message', 'status')
        }),
        ('Dates', {
            'fields': ('application_date',),
            'classes': ('collapse',),
        }),
        ('Files', {
            'fields': ('cv_file', 'business_license'),
            'classes': ('collapse',),
        }),
    )










