from django.db import models
from app_accounts.models import CustomUser
from app_posts.models import MaintenancePost, SpecialistApplication

class Notification(models.Model):
    NOTIFICATION_TYPES = (
        ('application_status', 'Application Status Change'),
        ('post_status', 'Post Status Change'),
        ('new_application', 'New Application'),
        ('job_completed', 'Job Completed'),
        ('job_approved', 'Job Approved'),
        ('job_rejected', 'Job Rejected'),
        ('new_post', 'New Maintenance Request'),
        ('post_updated', 'Maintenance Request Updated'),
        ('system', 'System Notification'),
        ('support_reply', 'Support Request Reply'),
        ('new_support_message', 'New Support Message'),
        ('new_rating', 'New Rating Received'),
    )
    
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    related_post = models.ForeignKey(MaintenancePost, on_delete=models.CASCADE, null=True, blank=True)
    related_application = models.ForeignKey(SpecialistApplication, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'notifications'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.notification_type} for {self.recipient.username}"
