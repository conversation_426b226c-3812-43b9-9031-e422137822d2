{% load static %}
<header>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="logo-text">Smart-IT</div>
                </div>
                <span class="brand-text">Maintenance</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <!-- Theme Switcher -->
                    <li class="nav-item dropdown me-2">
                        <button class="btn btn-link nav-link px-2 dropdown-toggle d-flex align-items-center" 
                                id="bd-theme" 
                                type="button" 
                                aria-expanded="false" 
                                data-bs-toggle="dropdown" 
                                data-bs-display="static">
                            <i class="fas fa-sun theme-icon-active me-1" id="theme-icon"></i>
                            <span class="d-lg-none ms-2" id="bd-theme-text">Toggle theme</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="bd-theme">
                            <li>
                                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light">
                                    <i class="fas fa-sun me-2"></i>
                                    Light
                                </button>
                            </li>
                            <li>
                                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark">
                                    <i class="fas fa-moon me-2"></i>
                                    Dark
                                </button>
                            </li>
                            <li>
                                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto">
                                    <i class="fas fa-circle-half-stroke me-2"></i>
                                    Auto
                                </button>
                            </li>
                        </ul>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if unread_notification_count > 0 %}
                                <span class="badge bg-danger">{{ unread_notification_count }}</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="min-width: 300px;">
                            <li>
                                <h6 class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>Notifications</span>
                                    {% if unread_notification_count > 0 %}
                                        <span class="badge bg-primary">{{ unread_notification_count }} unread</span>
                                    {% endif %}
                                </h6>
                            </li>
                            {% if user.user_type == 'admin' and unread_support_count > 0 %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin_support_messages' %}">
                                        <i class="fas fa-headset me-2"></i> Support Messages
                                        <span class="badge bg-danger float-end">{{ unread_support_count }}</span>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                            {% endif %}

                            <!-- Recent notifications preview -->
                            {% load notification_tags %}
                            {% get_recent_notifications user as recent_notifications %}
                            {% if recent_notifications %}
                                {% for notification in recent_notifications %}
                                <li>
                                    <a href="{% url 'notification_redirect' notification.id %}" class="dropdown-item small {% if not notification.is_read %}bg-light{% endif %} text-decoration-none" style="white-space: normal; padding: 12px 16px;">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="d-flex align-items-start flex-grow-1">
                                                <!-- Notification type icon -->
                                                <div class="me-2 mt-1">
                                                    {% if notification.notification_type == 'new_application' %}
                                                        <i class="fas fa-user-plus text-success"></i>
                                                    {% elif notification.notification_type == 'job_completed' %}
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    {% elif notification.notification_type == 'job_approved' %}
                                                        <i class="fas fa-thumbs-up text-success"></i>
                                                    {% elif notification.notification_type == 'job_rejected' %}
                                                        <i class="fas fa-times-circle text-danger"></i>
                                                    {% elif notification.notification_type == 'application_status' %}
                                                        <i class="fas fa-clipboard-list text-info"></i>
                                                    {% elif notification.notification_type == 'new_rating' %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% elif notification.notification_type == 'system' %}
                                                        <i class="fas fa-cog text-secondary"></i>
                                                    {% else %}
                                                        <i class="fas fa-bell text-primary"></i>
                                                    {% endif %}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <strong class="d-block text-body-emphasis">{{ notification.title|truncatechars:30 }}</strong>
                                                    <span class="text-body-secondary">{{ notification.message|truncatechars:55 }}</span>
                                                    <small class="d-block text-body-secondary mt-1">
                                                        <i class="fas fa-clock me-1"></i>{{ notification.created_at|timesince }} ago
                                                    </small>
                                                    {% if notification.related_post %}
                                                        <small class="d-block text-primary mt-1">
                                                            <i class="fas fa-arrow-right me-1"></i>View Maintenance Request
                                                        </small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            {% if not notification.is_read %}
                                                <span class="badge bg-warning ms-2">New</span>
                                            {% endif %}
                                        </div>
                                    </a>
                                </li>
                                {% if not forloop.last %}<li><hr class="dropdown-divider my-1"></li>{% endif %}
                                {% endfor %}
                                <li><hr class="dropdown-divider"></li>
                            {% else %}
                                <li><span class="dropdown-item-text text-body-secondary">No notifications yet</span></li>
                                <li><hr class="dropdown-divider"></li>
                            {% endif %}

                            <li><a class="dropdown-item text-center fw-bold" href="{% url 'notifications_list' %}">View All Notifications</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                    </li>
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                {% if user.profile_picture %}
                                    <img src="{{ user.get_profile_picture_base64 }}" alt="Profile" class="rounded-circle me-1" style="width: 25px; height: 25px; object-fit: cover;">
                                {% else %}
                                    <i class="fas fa-user-circle me-1"></i>
                                {% endif %}
                                {{ user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li>
                                    <a class="dropdown-item" href="{% url 'dashboard_redirect' %}">
                                        <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'profile' %}">
                                        <i class="fas fa-user-cog me-1"></i> Profile
                                    </a>
                                </li>
                                {% if user.is_staff %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin:index' %}">
                                        <i class="fas fa-user-shield me-1"></i> Admin Panel
                                    </a>
                                </li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="post" action="{% url 'logout' %}" class="dropdown-item p-0" style="background: none; border: none;">
                                        {% csrf_token %}
                                        <button type="submit" class="dropdown-item" style="background: none; border: none; width: 100%; text-align: left;">
                                            <i class="fas fa-sign-out-alt me-1"></i> Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register' %}">
                                <i class="fas fa-user-plus me-1"></i> Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
</header>

