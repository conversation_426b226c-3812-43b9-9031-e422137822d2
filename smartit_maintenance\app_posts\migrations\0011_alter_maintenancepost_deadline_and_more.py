# Generated by Django 5.2.1 on 2025-06-02 02:24

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_posts', '0010_maintenancepost_assigned_to_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='maintenancepost',
            name='deadline',
            field=models.DateTimeField(default=datetime.datetime(2025, 6, 9, 2, 24, 11, 17789, tzinfo=datetime.timezone.utc)),
        ),
        migrations.AlterField(
            model_name='maintenancepost',
            name='status',
            field=models.CharField(choices=[('open', 'Open'), ('pending_confirmation', 'Pending Confirmation'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('closed', 'Closed')], default='open', max_length=20),
        ),
        migrations.AlterField(
            model_name='specialistapplication',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('confirmed', 'Confirmed'), ('declined', 'Declined'), ('rejected', 'Rejected')], default='pending', max_length=20),
        ),
    ]
