{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Application Details</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='dashboard' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Application Overview Card -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Application for "{{ post.title }}"</h4>
                    <span class="badge bg-{% if application.status == 'pending' %}warning{% elif application.status == 'accepted' %}success{% elif application.status == 'confirmed' %}info{% elif application.status == 'declined' %}secondary{% else %}danger{% endif %} fs-6">
                        {{ application.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-muted mb-2">Application Message:</h6>
                            <p class="mb-3">{{ application.message|default:"No message provided." }}</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">Applied on:</small><br>
                                    <strong>{{ application.application_date|date:"F d, Y H:i" }}</strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">Post Category:</small><br>
                                    <span class="badge bg-info">{{ post.get_category_display }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex gap-2 flex-wrap">
                                {% if application.cv_file %}
                                    <a href="{{ application.cv_file.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-file-pdf me-1"></i> View CV
                                    </a>
                                {% endif %}
                                {% if application.business_license %}
                                    <a href="{{ application.business_license.url }}" class="btn btn-sm btn-outline-secondary" target="_blank">
                                        <i class="fas fa-certificate me-1"></i> Business License
                                    </a>
                                {% endif %}
                                <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye me-1"></i> View Post
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Specialist Profile Card -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>Specialist Profile
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Profile Picture and Basic Info -->
                        <div class="col-md-4 text-center mb-4">
                            {% if specialist.profile_picture %}
                                <img src="{{ specialist.get_profile_picture_base64 }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                            {% else %}
                                <div class="profile-placeholder mb-3">
                                    <i class="fas fa-user-circle fa-5x text-secondary"></i>
                                </div>
                            {% endif %}
                            <h5 class="mb-1">{{ specialist.get_full_name|default:specialist.username }}</h5>
                            <p class="text-muted mb-2">@{{ specialist.username }}</p>
                            <span class="badge bg-success">{{ specialist.get_user_type_display }}</span>
                            {% if specialist.category %}
                                <span class="badge bg-info">{{ specialist.get_category_display }}</span>
                            {% endif %}
                        </div>
                        
                        <!-- Profile Details -->
                        <div class="col-md-8">
                            <!-- Rating Summary -->
                            <div class="card mb-3 bg-light">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <div class="mb-2">
                                                {% if average_rating %}
                                                    {% include 'includes/rating_stars.html' with rating=average_rating show_text=True size='lg' %}
                                                {% else %}
                                                    <div class="text-muted">
                                                        <i class="fas fa-star-o fa-lg"></i>
                                                        <span class="ms-2">No ratings yet</span>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <small class="text-muted">Average Rating</small>
                                        </div>
                                        <div class="col-md-4">
                                            <h4 class="text-primary mb-1">{{ total_ratings }}</h4>
                                            <small class="text-muted">Total Reviews</small>
                                        </div>
                                        <div class="col-md-4">
                                            <h4 class="text-success mb-1">{{ completed_jobs }}</h4>
                                            <small class="text-muted">Completed Jobs</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Contact Information</h6>
                                    {% if specialist.email %}
                                        <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ specialist.email }}</p>
                                    {% endif %}
                                    {% if specialist.phone_number %}
                                        <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ specialist.phone_number }}</p>
                                    {% endif %}
                                    {% if specialist.address %}
                                        <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>{{ specialist.address }}</p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">Member Since</h6>
                                    <p><i class="fas fa-calendar me-2"></i>{{ specialist.date_joined|date:"F Y" }}</p>
                                </div>
                            </div>
                            
                            <!-- Bio -->
                            {% if specialist.bio %}
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2">About</h6>
                                    <p class="text-justify">{{ specialist.bio }}</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Jobs Card -->
            {% if recent_jobs %}
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>Recent Completed Jobs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Job Title</th>
                                    <th>Client</th>
                                    <th>Category</th>
                                    <th>Completed Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for job in recent_jobs %}
                                <tr>
                                    <td>{{ job.post.title }}</td>
                                    <td>{{ job.post.created_by.get_full_name|default:job.post.created_by.username }}</td>
                                    <td><span class="badge bg-secondary">{{ job.post.get_category_display }}</span></td>
                                    <td>{{ job.post.created_at|date:"M d, Y" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Customer Reviews Card -->
            {% if specialist_ratings %}
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Customer Reviews ({{ specialist_ratings.count }})
                    </h5>
                </div>
                <div class="card-body">
                    {% for rating in specialist_ratings %}
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <strong>{{ rating.rated_by.get_full_name|default:rating.rated_by.username }}</strong>
                                        <div class="mt-1">
                                            {% include 'includes/rating_stars.html' with rating=rating.rating show_text=True size='sm' %}
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ rating.created_at|date:"M d, Y" }}</small>
                                </div>
                                <p class="mb-1"><strong>Job:</strong> {{ rating.post.title }}</p>
                                {% if rating.comment %}
                                    <p class="mb-0 text-muted">{{ rating.comment }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons for Post Owner -->
            {% if user == post.created_by and application.status == 'pending' %}
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="{% url 'accept_application' application.id %}" class="btn btn-success" onclick="return confirm('Are you sure you want to accept this application?')">
                            <i class="fas fa-check me-1"></i> Accept Application
                        </a>
                        <a href="{% url 'reject_application' application.id %}" class="btn btn-danger" onclick="return confirm('Are you sure you want to reject this application?')">
                            <i class="fas fa-times me-1"></i> Reject Application
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
