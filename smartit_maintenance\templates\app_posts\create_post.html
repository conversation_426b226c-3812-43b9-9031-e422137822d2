{% extends 'base.html' %}
{% load static %}

{% block title %}Create Maintenance Request{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4">Create Maintenance Request</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='create_post' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Create Maintenance Request</h3>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            <p class="mb-0">{{ error }}</p>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="{{ form.title.id_for_label }}" class="form-label">Title <span class="text-danger">*</span></label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                <div class="invalid-feedback d-block">{{ form.title.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Provide a clear, descriptive title for your maintenance request</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="{{ form.description.id_for_label }}" class="form-label">Description <span class="text-danger">*</span></label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                <div class="invalid-feedback d-block">{{ form.description.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Describe the issue in detail, including any relevant information</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Category <span class="text-danger">*</span></label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                <div class="invalid-feedback d-block">{{ form.category.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Select the category that best matches your maintenance need</div>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.experience.id_for_label }}" class="form-label">Required Experience <span class="text-danger">*</span></label>
                                {{ form.experience }}
                                {% if form.experience.errors %}
                                <div class="invalid-feedback d-block">{{ form.experience.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Minimum experience level required for this task</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="{{ form.location.id_for_label }}" class="form-label">Location <span class="text-danger">*</span></label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                <div class="invalid-feedback d-block">{{ form.location.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Specify where the maintenance is needed</div>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.deadline.id_for_label }}" class="form-label">Deadline <span class="text-danger">*</span></label>
                                {{ form.deadline }}
                                {% if form.deadline.errors %}
                                <div class="invalid-feedback d-block">{{ form.deadline.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">When do you need this completed by?</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="{{ form.requester_type.id_for_label }}" class="form-label">Requester Type <span class="text-danger">*</span></label>
                                {{ form.requester_type }}
                                {% if form.requester_type.errors %}
                                <div class="invalid-feedback d-block">{{ form.requester_type.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Are you requesting this as an individual or on behalf of an organization?</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3 organization-fields" style="display: none;">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="{{ form.organization_name.id_for_label }}" class="form-label">Organization Name</label>
                                {{ form.organization_name }}
                                {% if form.organization_name.errors %}
                                <div class="invalid-feedback d-block">{{ form.organization_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.organization_department.id_for_label }}" class="form-label">Department (Optional)</label>
                                {{ form.organization_department }}
                                {% if form.organization_department.errors %}
                                <div class="invalid-feedback d-block">{{ form.organization_department.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'user_dashboard' %}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Tips for a Good Maintenance Request</h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Be specific about the issue you're experiencing</li>
                        <li>Include relevant details like model numbers or error messages</li>
                        <li>Mention any previous attempts to fix the issue</li>
                        <li>Set a realistic deadline based on the urgency of the problem</li>
                        <li>Provide clear location details to help specialists find you</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Show/hide organization fields based on requester type
    document.addEventListener('DOMContentLoaded', function() {
        const requesterTypeField = document.querySelector('#{{ form.requester_type.id_for_label }}');
        const organizationFields = document.querySelector('.organization-fields');
        
        function toggleOrganizationFields() {
            if (requesterTypeField.value === 'organization') {
                organizationFields.style.display = 'flex';
                document.querySelector('#{{ form.organization_name.id_for_label }}').required = true;
            } else {
                organizationFields.style.display = 'none';
                document.querySelector('#{{ form.organization_name.id_for_label }}').required = false;
            }
        }
        
        // Initial state
        toggleOrganizationFields();
        
        // Listen for changes
        requesterTypeField.addEventListener('change', toggleOrganizationFields);
        
        // Set min date for deadline
        const deadlineField = document.querySelector('#{{ form.deadline.id_for_label }}');
        if (deadlineField) {
            const today = new Date();
            const formattedDate = today.toISOString().slice(0, 16);
            deadlineField.setAttribute('min', formattedDate);
        }
    });
    
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>
{% endblock %}


