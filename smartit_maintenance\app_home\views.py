from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import SupportMessage

def home(request):
    return render(request, 'app_home/home.html')

@login_required
def dashboard_redirect(request):
    """
    Redirects users to the appropriate dashboard based on their user type
    """
    if request.user.user_type == 'admin':
        return redirect('admin_dashboard')
    elif request.user.user_type == 'specialist':
        return redirect('specialist_dashboard')
    elif request.user.user_type == 'user':
        return redirect('user_dashboard')
    else:
        # Fallback for unknown user types
        messages.warning(request, "Unknown user type. Redirecting to home.")
        return redirect('home')

def help_support(request):
    return render(request, 'app_home/help_support.html', {'active_tab': 'help'})

@login_required
def submit_support(request):
    if request.method == 'POST':
        subject = request.POST.get('subject')
        message_text = request.POST.get('message')
        
        if subject and message_text:
            # Create a new support message
            support_message = SupportMessage.objects.create(
                user=request.user,
                subject=subject,
                message=message_text
            )
            
            # Notify admins about the new support message
            support_message.notify_admins()
            
            messages.success(request, 'Your support request has been submitted successfully. Our team will get back to you soon.')
        else:
            messages.error(request, 'Please fill in all required fields.')
            
    return redirect('help_support')

@login_required
def my_support_messages(request):
    """View for users to see their support messages and admin replies"""
    support_messages = SupportMessage.objects.filter(user=request.user).order_by('-created_at')
    return render(request, 'app_home/my_support_messages.html', {
        'support_messages': support_messages,
        'active_tab': 'support_messages'
    })

@login_required
def admin_support_messages(request):
    """View for admins to see all support messages"""
    if request.user.user_type != 'admin':
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('dashboard_redirect')
    
    support_messages = SupportMessage.objects.all().order_by('-created_at')
    
    # Mark related notifications as read
    if hasattr(request.user, 'notifications'):
        request.user.notifications.filter(
            notification_type='new_support_message'
        ).update(is_read=True)
    
    return render(request, 'app_home/admin_support_messages.html', {
        'support_messages': support_messages,
        'active_tab': 'admin_support'
    })

@login_required
def admin_dashboard(request):
    """Dashboard view for admin users"""
    # Ensure the user is an admin
    if request.user.user_type != 'admin':
        messages.error(request, "You don't have permission to access the admin dashboard.")
        return redirect('home')
    
    # Add your admin dashboard logic here
    context = {
        'active_tab': 'dashboard'
    }
    return render(request, 'app_home/admin_dashboard.html', context)

