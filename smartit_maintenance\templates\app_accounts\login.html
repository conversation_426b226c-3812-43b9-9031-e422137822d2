{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card mt-5 mb-5">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Login</h3>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <p>Your username and password didn't match. Please try again.</p>
                    </div>
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_username" class="form-label">Username</label>
                            <input type="text" name="username" class="form-control" id="id_username" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_password" class="form-label">Password</label>
                            <input type="password" name="password" class="form-control" id="id_password" required>
                            <div class="mt-1">
                                <a href="{% url 'password_reset_request' %}" class="text-decoration-none small">Forgot your password?</a>
                            </div>
                        </div>
                        <input type="hidden" name="next" value="{{ request.GET.next }}">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </form>
                    
                    <div class="mt-3">
                        <p>Don't have an account? <a href="{% url 'register' %}">Register here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


