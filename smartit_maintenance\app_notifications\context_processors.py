def notification_count(request):
    """
    Context processor to add unread notification count to all templates
    """
    unread_notification_count = 0
    unread_support_count = 0
    
    if request.user.is_authenticated:
        try:
            # Get all unread notifications
            unread_notification_count = request.user.notifications.filter(is_read=False).count()
            
            # Add unread support message count for admin users
            if hasattr(request.user, 'user_type') and request.user.user_type == 'admin':
                unread_support_count = request.user.notifications.filter(
                    notification_type='new_support_message',
                    is_read=False
                ).count()
        except (AttributeError, Exception) as e:
            # Handle case where notifications aren't available
            pass
    
    return {
        'unread_notification_count': unread_notification_count,
        'unread_support_count': unread_support_count
    }
