# Generated by Django 5.2.1 on 2025-06-02 00:22

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_posts', '0002_maintenancepost_deadline'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='maintenancepost',
            name='urgency',
        ),
        migrations.AddField(
            model_name='maintenancepost',
            name='experience',
            field=models.CharField(choices=[('1_year', '1 Year'), ('2_years', '2 Years'), ('3_years', '3 Years'), ('4_years', '4 Years'), ('5_years', '5 Years')], default='1_year', max_length=20),
        ),
        migrations.AlterField(
            model_name='maintenancepost',
            name='deadline',
            field=models.DateTimeField(default=datetime.datetime(2025, 6, 9, 0, 22, 53, 899020, tzinfo=datetime.timezone.utc)),
        ),
    ]
