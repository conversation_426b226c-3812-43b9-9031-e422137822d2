{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Maintenance Request Details</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='dashboard' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Request Details Card -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ post.title }}</h4>
                    <span class="badge bg-{% if post.status == 'open' %}warning{% elif post.status == 'pending_confirmation' %}info{% elif post.status == 'in_progress' %}primary{% elif post.status == 'pending_approval' %}secondary{% elif post.status == 'completed' %}success{% else %}secondary{% endif %} fs-6">
                        {{ post.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <!-- Request Info Section -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <span class="badge bg-info me-2">{{ post.get_category_display }}</span>
                                <span class="badge bg-secondary me-2">Experience: {{ post.get_experience_display }}</span>
                            </div>
                            <small class="text-body-secondary">Posted on: {{ post.created_at|date:"M d, Y" }}</small>
                        </div>
                        
                        <!-- Request Description -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="mb-0 text-body-emphasis">Description</h5>
                            </div>
                            <div class="card-body">
                                <p>{{ post.description }}</p>
                            </div>
                        </div>
                        
                        <!-- Request Details -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="mb-0 text-body-emphasis">Request Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Status:</strong>
                                                <span class="badge bg-{% if post.status == 'open' %}warning{% elif post.status == 'pending_confirmation' %}info{% elif post.status == 'in_progress' %}primary{% elif post.status == 'pending_approval' %}secondary{% elif post.status == 'completed' %}success{% else %}secondary{% endif %}">
                                                    {{ post.get_status_display }}
                                                </span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Category:</strong>
                                                <span class="badge bg-info">{{ post.get_category_display }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Required Experience:</strong>
                                                <span class="badge bg-secondary">{{ post.get_experience_display }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            {% if post.location %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Location:</strong>
                                                <span>{{ post.location }}</span>
                                            </li>
                                            {% endif %}
                                            {% if post.requester_type == 'organization' %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Requester:</strong>
                                                <span class="badge bg-info">Organization</span>
                                            </li>
                                            {% endif %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Deadline:</strong>
                                                <span>{{ post.deadline|date:"M d, Y" }}</span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <strong>Posted:</strong>
                                                <span>{{ post.created_at|date:"M d, Y" }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Confirmation Section for Specialists -->
                    {% if user.user_type == 'specialist' and user_application and user_application.status == 'accepted' %}
                        <div class="alert alert-warning mb-4">
                            <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Maintenance Awaiting Your Confirmation</h5>
                            <p>The client has accepted your application for this maintenance task. Please confirm if you want to proceed with this maintenance.</p>
                            <hr>
                            <div class="d-flex justify-content-end">
                                <a href="{% url 'confirm_job' user_application.id %}" class="btn btn-success me-2">
                                    <i class="fas fa-check me-1"></i> Confirm Maintenance
                                </a>
                                <a href="{% url 'decline_job' user_application.id %}" class="btn btn-danger">
                                    <i class="fas fa-times me-1"></i> Decline
                                </a>
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Action Buttons for Post Owner -->
                    {% if user == post.created_by %}
                        <div class="d-flex justify-content-end">
                            {% if post.status == 'open' %}
                                <a href="{% url 'edit_post' post.id %}" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-edit me-1"></i> Edit
                                </a>
                                <a href="{% url 'close_post' post.id %}" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to close this request?')">
                                    <i class="fas fa-times-circle me-1"></i> Close Request
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <!-- Action Buttons for Assigned Specialist -->
                    {% if user == post.assigned_to and post.status == 'in_progress' %}
                        <div class="alert alert-primary mb-4">
                            <h5 class="alert-heading"><i class="fas fa-tools me-2"></i>Maintenance In Progress</h5>
                            <p>You are currently assigned to this maintenance task. Once you've completed the work, mark it as complete for client approval.</p>
                            <hr>
                            <div class="d-flex justify-content-end">
                                <a href="{% url 'mark_job_complete' post.id %}" class="btn btn-success" onclick="return confirm('Are you sure you want to mark this job as complete? This will notify the client for approval.')">
                                    <i class="fas fa-check-circle me-1"></i> Mark as Complete
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Applications Section for Post Owner -->
            {% if user == post.created_by and applications %}
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Applications ({{ applications.count }})</h4>
                    </div>
                    <div class="card-body p-0">
                        <div class="accordion" id="applicationsAccordion">
                            {% for application in applications %}
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading{{ application.id }}">
                                        <button class="accordion-button {% if forloop.counter > 1 %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ application.id }}" aria-expanded="{% if forloop.counter == 1 %}true{% else %}false{% endif %}" aria-controls="collapse{{ application.id }}">
                                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                <div class="d-flex align-items-center">
                                                    <span>{{ application.specialist.username }}</span>
                                                    {% if application.specialist.get_average_rating %}
                                                        <div class="ms-2">
                                                            {% include 'includes/rating_stars.html' with rating=application.specialist.get_average_rating show_text=True size='sm' %}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <span class="badge {% if application.status == 'pending' %}bg-warning{% elif application.status == 'accepted' %}bg-success{% else %}bg-danger{% endif %}">
                                                    {{ application.get_status_display }}
                                                </span>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse{{ application.id }}" class="accordion-collapse collapse {% if forloop.counter == 1 %}show{% endif %}" aria-labelledby="heading{{ application.id }}" data-bs-parent="#applicationsAccordion">
                                        <div class="accordion-body">
                                            <div class="mb-3">
                                                <h6>Application Message:</h6>
                                                <p>{{ application.message }}</p>
                                            </div>
                                            
                                            <div class="d-flex gap-2 mb-3">
                                                <a href="{% url 'application_detail' application.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-user me-1"></i> View Profile & Details
                                                </a>
                                                {% if application.cv_file %}
                                                    <a href="{{ application.cv_file.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-file-pdf me-1"></i> View CV
                                                    </a>
                                                {% endif %}
                                                {% if application.business_license %}
                                                    <a href="{{ application.business_license.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-file-contract me-1"></i> View Business License
                                                    </a>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">Applied on {{ application.application_date|date:"M d, Y" }}</small>
                                                {% if application.status == 'pending' %}
                                                    <div class="btn-group">
                                                        <a href="{% url 'accept_application' application.id %}" class="btn btn-sm btn-success">
                                                            <i class="fas fa-check me-1"></i> Accept
                                                        </a>
                                                        <a href="{% url 'reject_application' application.id %}" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-times me-1"></i> Reject
                                                        </a>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
            
            <!-- Apply Section for Specialists -->
            {% if user.user_type == 'specialist' and post.status == 'open' and post.category == user.category %}
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Apply for this Job</h4>
                    </div>
                    <div class="card-body">
                        {% if has_applied %}
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i> You have already applied for this job.
                            </div>
                        {% else %}
                            <a href="{% url 'apply_for_post' post.id %}" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane me-1"></i> Apply Now
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <!-- Rating Section for Completed Posts -->
            {% if post.status == 'completed' %}
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-star me-2"></i>Job Completed - Ratings</h4>
                    </div>
                    <div class="card-body">
                        <!-- Specialist Information -->
                        {% if post.assigned_to %}
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <h5 class="mb-3">Specialist: {{ post.assigned_to.get_full_name|default:post.assigned_to.username }}</h5>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-tools me-2"></i>{{ post.get_category_display }} Specialist
                                    </p>
                                    {% if post.assigned_to.phone_number %}
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-phone me-2"></i>{{ post.assigned_to.phone_number }}
                                        </p>
                                    {% endif %}
                                    {% if post.assigned_to.email %}
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-envelope me-2"></i>{{ post.assigned_to.email }}
                                        </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="badge bg-success fs-6 mb-2">
                                        <i class="fas fa-check-circle me-1"></i>Completed
                                    </div>
                                    <br>
                                    <small class="text-muted">Job completed successfully</small>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Rating Form for Post Owner -->
                        {% if user == post.created_by and can_rate_specialist and post.assigned_to %}
                            <div class="alert alert-info mb-4">
                                <h5 class="alert-heading"><i class="fas fa-star me-2"></i>Rate the Specialist</h5>
                                <p>How was your experience with {{ post.assigned_to.get_full_name|default:post.assigned_to.username }}? Your feedback helps other users make informed decisions.</p>
                                <hr>
                                <div class="d-flex justify-content-end">
                                    {% for application in applications %}
                                        {% if application.specialist == post.assigned_to %}
                                            <a href="{% url 'rate_specialist' application.id %}" class="btn btn-warning">
                                                <i class="fas fa-star me-1"></i> Rate Specialist
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Display Existing Ratings -->
                        {% if specialist_ratings %}
                            <div class="mb-4">
                                <h5 class="mb-3">Customer Reviews</h5>
                                {% for rating in specialist_ratings %}
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <strong>{{ rating.rated_by.get_full_name|default:rating.rated_by.username }}</strong>
                                                    <div class="mt-1">
                                                        {% include 'includes/rating_stars.html' with rating=rating.rating show_text=True size='sm' %}
                                                    </div>
                                                </div>
                                                <small class="text-muted">{{ rating.created_at|date:"M d, Y" }}</small>
                                            </div>
                                            {% if rating.comment %}
                                                <p class="mb-0 text-muted">{{ rating.comment }}</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% elif post.status == 'completed' and not can_rate_specialist %}
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-star-o fa-2x mb-2"></i>
                                <p>No ratings available yet for this completed job.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

<!-- Make sure this section is placed in a visible area of the template -->
{% if user == post.created_by and post.status == 'pending_approval' %}
    <div class="alert alert-warning mb-4">
        <h5 class="alert-heading"><i class="fas fa-clipboard-check me-2"></i>Maintenance Pending Approval</h5>
        <p>The specialist has marked this maintenance task as complete. Please review the work and either approve it or request additional changes.</p>
        <hr>
        <div class="d-flex justify-content-end">
            <a href="{% url 'approve_job' post.id %}" class="btn btn-success me-2" onclick="return confirm('Are you sure you want to approve this job? This will mark the maintenance as completed.')">
                <i class="fas fa-check-circle me-1"></i> Approve
            </a>
            <a href="{% url 'reject_job' post.id %}" class="btn btn-danger" onclick="return confirm('Are you sure you want to request additional changes? This will move the job back to in-progress status.')">
                <i class="fas fa-times-circle me-1"></i> Request Changes
            </a>
        </div>
    </div>
{% endif %}





