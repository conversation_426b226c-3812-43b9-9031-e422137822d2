{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">My Applications</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='my_applications' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">All Applications</h5>
                </div>
                <div class="card-body">
                    {% if applications %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Post Title</th>
                                        <th>Status</th>
                                        <th>Date Applied</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for application in applications %}
                                    <tr>
                                        <td>{{ application.post.title }}</td>
                                        <td>
                                            {% if application.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif application.status == 'accepted' %}
                                                <span class="badge bg-success">Accepted</span>
                                            {% elif application.status == 'rejected' %}
                                                <span class="badge bg-danger">Rejected</span>
                                            {% elif application.status == 'completed' %}
                                                <span class="badge bg-info">Completed</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ application.application_date|date:"M d, Y" }}</td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <a href="{% url 'application_detail' application.id %}" class="btn btn-sm btn-primary">View Application</a>
                                                <a href="{% url 'post_detail' application.post.id %}" class="btn btn-sm btn-outline-primary">View Post</a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">You haven't applied to any maintenance requests yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}




