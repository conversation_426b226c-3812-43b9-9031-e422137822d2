# Generated by Django 5.2.1 on 2025-06-02 01:37

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_posts', '0009_alter_maintenancepost_deadline'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='maintenancepost',
            name='assigned_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_posts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='maintenancepost',
            name='deadline',
            field=models.DateTimeField(default=datetime.datetime(2025, 6, 9, 1, 37, 39, 23396, tzinfo=datetime.timezone.utc)),
        ),
    ]
