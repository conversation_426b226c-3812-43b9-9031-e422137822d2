{% extends 'base.html' %}

{% block content %}
<div class="hero-section bg-primary text-white py-5">
    <div class="container text-center">
        <h1 class="display-4">Smart-IT Maintenance Portal</h1>
        <p class="lead">Efficient IT maintenance solutions at your fingertips</p>
        <div class="mt-4">
            {% if not user.is_authenticated %}
                <a href="{% url 'register' %}" class="btn btn-light btn-lg mx-2">Get Started</a>
                <a href="{% url 'login' %}" class="btn btn-outline-light btn-lg mx-2">Login</a>
            {% else %}
                <a href="{% url 'dashboard_redirect' %}" class="btn btn-light btn-lg mx-2">Go to Dashboard</a>
            {% endif %}
        </div>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-tools fa-3x text-primary mb-3"></i>
                    <h3 class="card-title">For Users</h3>
                    <p class="card-text">Submit maintenance requests and track their progress in real-time.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-user-cog fa-3x text-primary mb-3"></i>
                    <h3 class="card-title">For Specialists</h3>
                    <p class="card-text">Find maintenance tasks in your expertise area and apply to resolve them.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                    <h3 class="card-title">For Admins</h3>
                    <p class="card-text">Manage the platform, oversee maintenance tasks, and ensure quality service.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
