from django.core.management.base import BaseCommand
import os

class Command(BaseCommand):
    help = "Fixes the URLs in app_posts/urls.py"

    def handle(self, *args, **options):
        # Get the project root directory
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        
        # Path to the urls.py file
        urls_path = os.path.join(project_root, 'app_posts', 'urls.py')
        
        # Check if the file exists
        if not os.path.exists(urls_path):
            self.stdout.write(self.style.ERROR(f"File not found: {urls_path}"))
            return
        
        # Read the current content
        with open(urls_path, 'r') as file:
            content = file.read()
        
        # Replace the problematic line
        if "path('create/', views.create_post, name='create_post')," in content:
            content = content.replace(
                "path('create/', views.create_post, name='create_post'),",
                "path('create/', views.CreateMaintenancePostView.as_view(), name='create_post'),"
            )
            
            # Write the updated content
            with open(urls_path, 'w') as file:
                file.write(content)
            
            self.stdout.write(self.style.SUCCESS(f"Successfully updated {urls_path}"))
        else:
            self.stdout.write(self.style.WARNING(f"Could not find the line to replace in {urls_path}"))
            self.stdout.write(self.style.WARNING(f"Current content:\n{content}"))