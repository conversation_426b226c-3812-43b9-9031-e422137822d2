{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Notifications</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='notifications' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">My Notifications</h4>
                        {% if page_obj.paginator.count > 0 %}
                        <small class="text-light">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} notifications
                            {% if unread_count > 0 %}
                                ({{ unread_count }} unread)
                            {% endif %}
                        </small>
                        {% endif %}
                    </div>
                    {% if notifications %}
                    <a href="{% url 'mark_all_notifications_read' %}" class="btn btn-sm btn-light">Mark All as Read</a>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if notifications %}
                        <div class="list-group">
                            {% for notification in notifications %}
                                <div class="list-group-item {% if not notification.is_read %}list-group-item-warning{% endif %} mb-2">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h5 class="mb-0">{{ notification.title }}</h5>
                                        <small class="text-muted">{{ notification.created_at|date:"M d, Y H:i" }}</small>
                                    </div>
                                    <p class="mb-2">{{ notification.message }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-info">{{ notification.get_notification_type_display }}</span>
                                            {% if notification.related_post %}
                                                <a href="{% url 'notification_redirect' notification.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="fas fa-external-link-alt me-1"></i>View Related Post
                                                </a>
                                            {% elif notification.related_application %}
                                                <a href="{% url 'notification_redirect' notification.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="fas fa-external-link-alt me-1"></i>View Application
                                                </a>
                                            {% endif %}
                                        </div>
                                        {% if not notification.is_read %}
                                            <a href="{% url 'mark_notification_read' notification.id %}" class="btn btn-sm btn-outline-secondary">
                                                Mark as Read
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Notifications pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}

                    {% else %}
                        <div class="alert alert-info mb-0">
                            <p class="mb-0">You don't have any notifications yet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
