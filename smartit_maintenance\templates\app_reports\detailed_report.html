<!DOCTYPE html>
<html>
<head>
    <title>Smart-IT Maintenance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { border: 1px solid #ddd; padding: 15px; text-align: center; background-color: #f8f9fa; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .section-title { font-size: 1.5em; margin-top: 30px; margin-bottom: 15px; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .print-button { margin-bottom: 20px; }
        @media print { .print-button { display: none; } }
    </style>
</head>
<body>
    <div class="print-button">
        <button onclick="window.print()" style="background-color: #007bff; color: white; border: none; padding: 10px 20px; cursor: pointer;">
            Print Report
        </button>
        <button onclick="window.history.back()" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; cursor: pointer; margin-left: 10px;">
            Back to Dashboard
        </button>
    </div>

    <div class="header">
        <h1>Smart-IT Maintenance System</h1>
        <h2>Detailed Analytics Report</h2>
        <p><strong>Report Period:</strong> {{ report_data.start_date }} to {{ report_data.end_date }}</p>
        <p><strong>Generated on:</strong> {% now "F d, Y H:i" %}</p>
    </div>

    <!-- Executive Summary -->
    <div class="section-title">Executive Summary</div>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ report_data.posts_created }}</div>
            <div>Posts Created</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ report_data.applications_submitted }}</div>
            <div>Applications Submitted</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ report_data.posts_completed }}</div>
            <div>Posts Completed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ report_data.completion_rate|floatformat:1 }}%</div>
            <div>Completion Rate</div>
        </div>
    </div>

    <!-- Posts by Category -->
    <div class="section-title">Posts by Category</div>
    <table class="table">
        <thead>
            <tr>
                <th>Category</th>
                <th>Number of Posts</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {% for category in report_data.posts_by_category %}
            <tr>
                <td>{{ category.category|capfirst }}</td>
                <td>{{ category.count }}</td>
                <td>{% widthratio category.count report_data.posts_created 100 %}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Posts by Status -->
    <div class="section-title">Posts by Status</div>
    <table class="table">
        <thead>
            <tr>
                <th>Status</th>
                <th>Number of Posts</th>
                <th>Percentage</th>
            </tr>
        </thead>
        <tbody>
            {% for status in report_data.posts_by_status %}
            <tr>
                <td>{{ status.status|capfirst }}</td>
                <td>{{ status.count }}</td>
                <td>{% widthratio status.count report_data.posts_created 100 %}%</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Top Categories -->
    <div class="section-title">Top 5 Categories by Volume</div>
    <table class="table">
        <thead>
            <tr>
                <th>Rank</th>
                <th>Category</th>
                <th>Posts</th>
            </tr>
        </thead>
        <tbody>
            {% for category in report_data.top_categories %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ category.category|capfirst }}</td>
                <td>{{ category.count }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Recent Posts -->
    {% if posts_in_period %}
    <div class="section-title">Recent Posts (Sample)</div>
    <table class="table">
        <thead>
            <tr>
                <th>Title</th>
                <th>Category</th>
                <th>Status</th>
                <th>Created By</th>
                <th>Created Date</th>
            </tr>
        </thead>
        <tbody>
            {% for post in posts_in_period %}
            <tr>
                <td>{{ post.title|truncatechars:50 }}</td>
                <td>{{ post.get_category_display }}</td>
                <td>{{ post.get_status_display }}</td>
                <td>{{ post.created_by.username }}</td>
                <td>{{ post.created_at|date:"M d, Y H:i" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}

    <!-- Recent Applications -->
    {% if applications_in_period %}
    <div class="section-title">Recent Applications (Sample)</div>
    <table class="table">
        <thead>
            <tr>
                <th>Post Title</th>
                <th>Specialist</th>
                <th>Status</th>
                <th>Application Date</th>
            </tr>
        </thead>
        <tbody>
            {% for application in applications_in_period %}
            <tr>
                <td>{{ application.post.title|truncatechars:40 }}</td>
                <td>{{ application.specialist.username }}</td>
                <td>{{ application.get_status_display }}</td>
                <td>{{ application.application_date|date:"M d, Y H:i" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}

    <!-- Key Insights -->
    <div class="section-title">Key Insights</div>
    <ul>
        <li><strong>Total Activity:</strong> {{ report_data.posts_created }} new maintenance requests were created during this period.</li>
        <li><strong>Engagement:</strong> {{ report_data.applications_submitted }} applications were submitted by specialists.</li>
        <li><strong>Completion Rate:</strong> {{ report_data.completion_rate|floatformat:1 }}% of posts were successfully completed.</li>
        {% if report_data.top_categories %}
        <li><strong>Most Popular Category:</strong> {{ report_data.top_categories.0.category|capfirst }} with {{ report_data.top_categories.0.count }} posts.</li>
        {% endif %}
        <li><strong>System Performance:</strong> The platform is actively facilitating connections between users and specialists.</li>
    </ul>

    <!-- Recommendations -->
    <div class="section-title">Recommendations</div>
    <ul>
        {% if report_data.completion_rate < 70 %}
        <li>Consider implementing measures to improve the completion rate, which is currently at {{ report_data.completion_rate|floatformat:1 }}%.</li>
        {% endif %}
        {% if report_data.top_categories %}
        <li>Focus on recruiting more specialists in the {{ report_data.top_categories.0.category|capfirst }} category due to high demand.</li>
        {% endif %}
        <li>Continue monitoring user engagement and specialist response times to maintain service quality.</li>
        <li>Consider implementing user feedback mechanisms to improve the platform experience.</li>
    </ul>

    <div style="margin-top: 50px; text-align: center; color: #666; font-size: 0.9em;">
        <p>This report was automatically generated by the Smart-IT Maintenance System.</p>
        <p>For questions or additional analysis, please contact the system administrator.</p>
    </div>
</body>
</html>
