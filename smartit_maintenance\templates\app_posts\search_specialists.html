{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">
        <i class="fas fa-users me-2"></i>Find Specialists
    </h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='specialists' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Search Form -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Search & Filter Specialists
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" id="specialist-search-form">
                        <div class="row">
                            <!-- Search Query -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.search.id_for_label }}" class="form-label">Search</label>
                                {{ form.search }}
                            </div>
                            
                            <!-- Category Filter -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                                {{ form.category }}
                            </div>
                            
                            <!-- Rating Filter -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.min_rating.id_for_label }}" class="form-label">Minimum Rating</label>
                                {{ form.min_rating }}
                            </div>
                            
                            <!-- Sort By -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.sort_by.id_for_label }}" class="form-label">Sort By</label>
                                {{ form.sort_by }}
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-success me-2">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="{% url 'search_specialists' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>Available Specialists
                    </h5>
                    <div class="text-end">
                        <span class="badge bg-light text-dark">{{ total_results }} found</span>
                        {% if page_obj.paginator.count > 0 %}
                        <br><small class="text-light">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} specialists
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if specialists %}
                        <div class="row">
                            {% for specialist in specialists %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start mb-3">
                                            <!-- Profile Picture -->
                                            <div class="me-3">
                                                {% if specialist.profile_picture %}
                                                    <img src="{{ specialist.get_profile_picture_base64 }}" alt="Profile" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                                                {% else %}
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <!-- Specialist Info -->
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">{{ specialist.get_full_name|default:specialist.username }}</h6>
                                                <p class="text-muted mb-1">@{{ specialist.username }}</p>
                                                {% if specialist.category %}
                                                    <span class="badge bg-primary">{{ specialist.get_category_display }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <!-- Rating and Stats -->
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <div class="border-end">
                                                    {% if specialist.avg_rating %}
                                                        {% include 'includes/rating_stars.html' with rating=specialist.avg_rating show_text=True size='sm' %}
                                                    {% else %}
                                                        <small class="text-muted">No ratings</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="border-end">
                                                    <strong class="d-block">{{ specialist.total_ratings }}</strong>
                                                    <small class="text-muted">Reviews</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <strong class="d-block">{{ specialist.completed_jobs_count }}</strong>
                                                <small class="text-muted">Jobs</small>
                                            </div>
                                        </div>
                                        
                                        <!-- Bio -->
                                        {% if specialist.bio %}
                                        <p class="card-text text-muted small">{{ specialist.bio|truncatechars:100 }}</p>
                                        {% endif %}
                                        
                                        <!-- Contact Info -->
                                        <div class="mb-2">
                                            {% if specialist.email %}
                                            <small class="text-muted d-block">
                                                <i class="fas fa-envelope me-1"></i>{{ specialist.email }}
                                            </small>
                                            {% endif %}
                                            {% if specialist.phone_number %}
                                            <small class="text-muted d-block">
                                                <i class="fas fa-phone me-1"></i>{{ specialist.phone_number }}
                                            </small>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- Member Since -->
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>Member since {{ specialist.date_joined|date:"M Y" }}
                                        </small>
                                    </div>
                                    <div class="card-footer">
                                        <div class="d-flex justify-content-between">
                                            <button class="btn btn-outline-primary btn-sm" onclick="viewSpecialistProfile('{{ specialist.username }}')">
                                                <i class="fas fa-user me-1"></i>View Profile
                                            </button>
                                            {% if user.user_type == 'user' %}
                                            <button class="btn btn-success btn-sm" onclick="contactSpecialist('{{ specialist.email }}')">
                                                <i class="fas fa-envelope me-1"></i>Contact
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Specialists pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                        
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No specialists found</h5>
                            <p class="text-muted">Try adjusting your search criteria or filters.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('specialist-search-form');
    const selects = form.querySelectorAll('select');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
});

function viewSpecialistProfile(username) {
    // This could redirect to a specialist profile page
    alert('Profile view for ' + username + ' - Feature coming soon!');
}

function contactSpecialist(email) {
    // Open email client
    window.location.href = 'mailto:' + email;
}
</script>
{% endblock %}
