import traceback
from django.http import HttpResponse
import inspect
from django.conf import settings
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout, update_session_auth_hash, get_user_model
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_POST
from django.contrib.auth.forms import PasswordChangeForm, SetPasswordForm
from .forms import UserRegistrationForm, ProfilePictureForm, ProfileUpdateForm
from django.contrib import messages
from .utils import send_sms_verification
from .sms import send_welcome_sms, send_password_reset_otp
from .models import PasswordResetOTP
from django import forms
from django.core.exceptions import MultipleObjectsReturned

class PhoneNumberForm(forms.Form):
    """Form for entering phone number for password reset"""
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+255XXXXXXXXX'})
    )

class OTPVerificationForm(forms.Form):
    """Form for verifying OTP"""
    otp_code = forms.CharField(
        max_length=6,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '6-digit OTP'})
    )

def register(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            
            # If user is not a specialist, set category to None
            if user.user_type != 'specialist':
                user.category = None
                
            user.save()
            
            # Send welcome SMS if phone number is provided
            if user.phone_number:
                try:
                    from .sms import send_welcome_sms
                    sms_response = send_welcome_sms(user, user.phone_number)
                    print(f"Welcome SMS response: {sms_response}")
                    
                    if sms_response.get('status') == 'success':
                        messages.success(request, 'Account created successfully! A welcome message has been sent to your phone.')
                    else:
                        messages.success(request, 'Account created successfully! You can now log in.')
                        print(f"Failed to send welcome SMS: {sms_response}")
                except Exception as e:
                    messages.success(request, 'Account created successfully! You can now log in.')
                    print(f"Exception sending welcome SMS: {str(e)}")
            else:
                messages.success(request, 'Account created successfully! You can now log in.')
            
            return redirect('login')
    else:
        form = UserRegistrationForm()
    
    return render(request, 'app_accounts/register.html', {'form': form})

@login_required
def profile(request):
    if request.method == 'POST':
        if 'profile_picture' in request.FILES:
            profile_form = ProfilePictureForm(request.POST, request.FILES)
            if profile_form.is_valid():
                request.user.set_profile_picture(request.FILES['profile_picture'])
                request.user.save()
                messages.success(request, 'Profile picture updated successfully!')
                return redirect('profile')
        else:
            form = ProfileUpdateForm(request.POST, instance=request.user)
            if form.is_valid():
                form.save()
                messages.success(request, 'Your profile has been updated!')
                return redirect('profile')
    else:
        form = ProfileUpdateForm(instance=request.user)
        profile_form = ProfilePictureForm()
    
    # Get additional data for specialists
    context = {
        'form': form,
        'profile_picture_form': profile_form
    }
    
    if request.user.user_type == 'specialist':
        from app_posts.models import SpecialistApplication
        # Count completed jobs
        completed_jobs_count = SpecialistApplication.objects.filter(
            specialist=request.user,
            post__status='completed'
        ).count()
        
        # Count active jobs
        active_jobs_count = SpecialistApplication.objects.filter(
            specialist=request.user,
            status='confirmed',
            post__status='in_progress'
        ).count()
        
        # Get average rating if available
        try:
            from django.db.models import Avg, Count
            from app_posts.models import SpecialistRating
            
            rating_data = SpecialistRating.objects.filter(
                specialist=request.user
            ).aggregate(
                average=Avg('rating'),
                count=Count('id')
            )
            
            context.update({
                'average_rating': rating_data['average'],
                'ratings_count': rating_data['count']
            })
        except ImportError:
            # SpecialistRating model doesn't exist yet
            context.update({
                'average_rating': None,
                'ratings_count': 0
            })
        
        context.update({
            'completed_jobs_count': completed_jobs_count,
            'active_jobs_count': active_jobs_count
        })
    
    # Get additional data for regular users
    elif request.user.user_type == 'user':
        from app_posts.models import MaintenancePost
        
        # Count total posts
        total_posts_count = MaintenancePost.objects.filter(
            created_by=request.user
        ).count()
        
        # Count active posts
        active_posts_count = MaintenancePost.objects.filter(
            created_by=request.user,
            status__in=['open', 'in_progress', 'pending_approval']
        ).count()
        
        # Count completed posts
        completed_posts_count = MaintenancePost.objects.filter(
            created_by=request.user,
            status='completed'
        ).count()
        
        context.update({
            'total_posts_count': total_posts_count,
            'active_posts_count': active_posts_count,
            'completed_posts_count': completed_posts_count
        })
    
    return render(request, 'app_accounts/profile.html', context)

@login_required
def edit_profile(request):
    if request.method == 'POST':
        form_type = request.POST.get('form_type')
        
        if form_type == 'profile_update':
            form = ProfileUpdateForm(request.POST, instance=request.user)
            password_form = PasswordChangeForm(request.user)
            
            if form.is_valid():
                form.save()
                messages.success(request, 'Your profile has been updated!')
                return redirect('profile')
        
        elif form_type == 'password_change':
            form = ProfileUpdateForm(instance=request.user)
            password_form = PasswordChangeForm(request.user, request.POST)
            
            if password_form.is_valid():
                user = password_form.save()
                # Important: update the session to prevent logging out
                update_session_auth_hash(request, user)
                messages.success(request, 'Your password was successfully updated!')
                return redirect('profile')
    else:
        form = ProfileUpdateForm(instance=request.user)
        password_form = PasswordChangeForm(request.user)
    
    return render(request, 'app_accounts/edit_profile.html', {
        'form': form,
        'password_form': password_form
    })

@login_required
def logout_view(request):
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('home')

# Add this new view for changing password
@login_required
def change_password(request):
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            # Important: update the session to prevent logging out
            update_session_auth_hash(request, user)
            messages.success(request, 'Your password was successfully updated!')
            return redirect('profile')
        else:
            messages.error(request, 'Please correct the error below.')
    else:
        form = PasswordChangeForm(request.user)
    return render(request, 'app_accounts/change_password.html', {'form': form})

def password_reset_request(request):
    """View for requesting a password reset via phone number"""
    if request.method == 'POST':
        form = PhoneNumberForm(request.POST)
        if form.is_valid():
            phone_number = form.cleaned_data['phone_number']
            
            # Try to find a user with this phone number
            User = get_user_model()
            
            try:
                # Use raw SQL to get the first user with this phone number
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT id FROM app_accounts_customuser WHERE phone_number = %s LIMIT 1",
                        [phone_number]
                    )
                    row = cursor.fetchone()
                
                if not row:
                    messages.error(request, "No user found with this phone number.")
                    return render(request, 'app_accounts/password_reset_request.html', {'form': form})
                
                user_id = row[0]
                user = User.objects.get(id=user_id)
                
                # Debug: Print user info
                print(f"Selected user: {user.username} (ID: {user.id})")
                
                # Generate OTP
                otp_obj = PasswordResetOTP.generate_otp(user)
                
                # Send OTP via SMS
                sms_response = send_password_reset_otp(user, phone_number, otp_obj.otp_code)
                
                # Log the SMS response for debugging
                print(f"SMS Response: {sms_response}")
                
                if sms_response.get('status') == 'success':
                    # Store user_id in session for the next step
                    request.session['reset_user_id'] = user.id
                    messages.success(request, f"An OTP has been sent to {phone_number}. Please enter it to reset your password.")
                    return redirect('password_reset_verify_otp')
                else:
                    # For development/testing - show the OTP in the error message
                    if settings.DEBUG:
                        messages.error(request, f"Failed to send OTP. For testing, use this OTP: {otp_obj.otp_code}")
                        request.session['reset_user_id'] = user.id
                        return redirect('password_reset_verify_otp')
                    else:
                        messages.error(request, f"Failed to send OTP. Error: {sms_response.get('message', 'Unknown error')}. Please try again.")
            except MultipleObjectsReturned:
                messages.error(request, "Multiple accounts found with this phone number. Please contact support.")
                return render(request, 'app_accounts/password_reset_request.html', {'form': form})
            except Exception as e:
                # Debug: Print any exceptions
                print(f"Exception in password_reset_request: {str(e)}")
                import traceback
                traceback.print_exc()
                messages.error(request, "An error occurred while processing your request. Please try again.")
    else:
        form = PhoneNumberForm()
    
    return render(request, 'app_accounts/password_reset_request.html', {'form': form})

def password_reset_verify_otp(request):
    """View for verifying OTP"""
    # Check if we have a user_id in session
    user_id = request.session.get('reset_user_id')
    if not user_id:
        messages.error(request, "Password reset session expired. Please start again.")
        return redirect('password_reset_request')
    
    User = get_user_model()
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, "User not found. Please try again.")
        return redirect('password_reset_request')
    
    if request.method == 'POST':
        form = OTPVerificationForm(request.POST)
        if form.is_valid():
            otp_code = form.cleaned_data['otp_code']
            
            # Check if OTP is valid
            try:
                otp_obj = PasswordResetOTP.objects.get(user=user, otp_code=otp_code, is_used=False)
                
                if otp_obj.is_valid():
                    # Mark OTP as used
                    otp_obj.is_used = True
                    otp_obj.save()
                    
                    # Store in session that OTP is verified
                    request.session['otp_verified'] = True
                    
                    return redirect('password_reset_confirm')
                else:
                    messages.error(request, "OTP has expired. Please request a new one.")
            except PasswordResetOTP.DoesNotExist:
                messages.error(request, "Invalid OTP. Please try again.")
    else:
        form = OTPVerificationForm()
    
    return render(request, 'app_accounts/password_reset_verify_otp.html', {'form': form})

def password_reset_confirm(request):
    """View for setting a new password after OTP verification"""
    # Check if we have a user_id in session and OTP is verified
    user_id = request.session.get('reset_user_id')
    otp_verified = request.session.get('otp_verified')
    
    if not user_id or not otp_verified:
        messages.error(request, "Invalid password reset session. Please start again.")
        return redirect('password_reset_request')
    
    User = get_user_model()
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        messages.error(request, "User not found. Please try again.")
        return redirect('password_reset_request')
    
    if request.method == 'POST':
        form = SetPasswordForm(user, request.POST)
        if form.is_valid():
            form.save()
            
            # Clear session data
            if 'reset_user_id' in request.session:
                del request.session['reset_user_id']
            if 'otp_verified' in request.session:
                del request.session['otp_verified']
            
            messages.success(request, "Your password has been reset successfully. You can now log in with your new password.")
            return redirect('password_reset_complete')
    else:
        form = SetPasswordForm(user)
    
    return render(request, 'app_accounts/password_reset_confirm.html', {'form': form})

def password_reset_complete(request):
    """View for password reset completion"""
    return render(request, 'app_accounts/password_reset_complete.html')

@login_required
def dashboard_redirect(request):
    """
    Redirects users to the appropriate dashboard based on their user type
    """
    user = request.user

    if user.user_type == 'specialist':
        return redirect('specialist_dashboard')  # Redirect specialists to their dashboard
    elif user.user_type == 'user':
        return redirect('user_dashboard')  # Redirect regular users to their dashboard
    elif user.user_type == 'admin' or user.is_staff or user.is_superuser:
        # Redirect admin users to Django admin panel
        return redirect('/admin/')
    else:
        # Fallback for other user types
        return redirect('home')  # Redirect to home page

@require_POST
def admin_logout_view(request):
    """
    Custom logout view for admin that redirects to the main login page
    """
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('login')  # Changed from 'admin:login' to 'login'



