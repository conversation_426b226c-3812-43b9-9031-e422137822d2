from django.test import TestCase
from django.contrib.auth import get_user_model
from .forms import UserRegistrationForm
from .models import CustomUser

class UserRegistrationFormTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.User = get_user_model()
        # Create a user with a phone number
        self.existing_user = self.User.objects.create_user(
            username='existing_user',
            email='<EMAIL>',
            password='testpass123',
            phone_number='+255123456789',  # Valid 13-character format
            user_type='user'
        )

    def test_phone_number_duplicate_validation(self):
        """Test that duplicate phone numbers are rejected"""
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '+255123456789',  # Same as existing user
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('phone_number', form.errors)
        self.assertIn('This phone number is already registered', str(form.errors['phone_number']))

    def test_phone_number_unique_validation_passes(self):
        """Test that unique phone numbers are accepted"""
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '+255987654321',  # Different from existing user, valid format
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_phone_number_required(self):
        """Test that phone number is required during registration"""
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '',  # Empty phone number
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('phone_number', form.errors)
        self.assertIn('This field is required', str(form.errors['phone_number']))

    def test_phone_number_format_validation(self):
        """Test phone number format validation"""
        # Test cases for invalid phone numbers
        invalid_phone_numbers = [
            ('0757577419', 'Phone number cannot start with 0'),  # Starts with 0
            ('+254123456789', 'Phone number must start with +255'),  # Wrong country code
            ('+255123', 'Phone number must be exactly 13 characters'),  # Too short
            ('+25512345678901', 'Phone number must be exactly 13 characters'),  # Too long
            ('+255abc123456', 'Phone number must contain only digits'),  # Contains letters
            ('255123456789', 'Phone number must start with +255'),  # Missing +
        ]

        for phone_number, expected_error in invalid_phone_numbers:
            with self.subTest(phone_number=phone_number):
                form_data = {
                    'username': 'newuser',
                    'email': '<EMAIL>',
                    'password1': 'testpass123',
                    'password2': 'testpass123',
                    'phone_number': phone_number,
                    'user_type': 'user',
                }
                form = UserRegistrationForm(data=form_data)
                self.assertFalse(form.is_valid())
                self.assertIn('phone_number', form.errors)
                # Check if any of the expected error keywords are in the error message
                error_message = str(form.errors['phone_number'])
                self.assertTrue(any(keyword in error_message for keyword in expected_error.split()))

    def test_phone_number_valid_format(self):
        """Test that valid phone number format is accepted"""
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '+255757577419',  # Valid format
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_username_duplicate_validation(self):
        """Test that duplicate usernames are rejected"""
        form_data = {
            'username': 'existing_user',  # Same as existing user
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '+255111222333',  # Unique phone number
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('username', form.errors)
        self.assertIn('Username already used', str(form.errors['username']))

    def test_username_unique_validation_passes(self):
        """Test that unique usernames are accepted"""
        form_data = {
            'username': 'uniqueusername123',  # Unique username
            'email': '<EMAIL>',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'phone_number': '+255444555666',  # Unique phone number
            'user_type': 'user',
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())
