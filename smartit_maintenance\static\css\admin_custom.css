/* Base theme variables */
:root {
    /* Primary brand colors */
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #7209b7;
    --success-color: #06d6a0;
    --info-color: #4cc9f0;
    --warning-color: #f9c74f;
    --danger-color: #ef476f;
    
    /* Light mode specific */
    --admin-bg: #f8f9fa;
    --admin-text: #212529;
    --admin-card-bg: #ffffff;
    --admin-border-color: #e9ecef;
    --admin-header-bg: linear-gradient(135deg, #4361ee, #3a0ca3);
    --admin-header-text: #ffffff;
    --admin-nav-bg: #ffffff;
    --admin-nav-text: #495057;
    --admin-nav-hover-bg: rgba(67, 97, 238, 0.1);
    --admin-nav-hover-text: #4361ee;
    --admin-module-header-bg: linear-gradient(to right, #f8f9fa, #e9ecef);
    --admin-module-header-text: #212529;
}

/* Dark mode variables */
[data-bs-theme="dark"] {
    --admin-bg: #121212;
    --admin-text: #e9ecef;
    --admin-card-bg: #1e1e1e;
    --admin-border-color: #333333;
    --admin-header-bg: linear-gradient(135deg, #4361ee, #3a0ca3);
    --admin-header-text: #ffffff;
    --admin-nav-bg: #1e1e1e;
    --admin-nav-text: #e9ecef;
    --admin-nav-hover-bg: rgba(67, 97, 238, 0.2);
    --admin-nav-hover-text: #4cc9f0;
    --admin-module-header-bg: linear-gradient(to right, #1e1e1e, #2d2d2d);
    --admin-module-header-text: #e9ecef;
    --admin-input-bg: #2b3035;
    --admin-input-text: #e9ecef;
    --admin-input-border: #495057;
    --admin-input-focus-border: #4361ee;
}

/* Apply theme variables to Django admin elements */
[data-bs-theme="dark"] {
    background-color: var(--admin-bg);
    color: var(--admin-text);
}

[data-bs-theme="dark"] #header {
    background: var(--admin-header-bg);
    color: var(--admin-header-text);
}

[data-bs-theme="dark"] #content {
    background-color: var(--admin-bg);
    color: var(--admin-text);
}

[data-bs-theme="dark"] .module {
    background-color: var(--admin-card-bg);
    border-color: var(--admin-border-color);
}

[data-bs-theme="dark"] .module h2,
[data-bs-theme="dark"] .module caption {
    background: var(--admin-module-header-bg);
    color: var(--admin-module-header-text);
}

[data-bs-theme="dark"] a:link,
[data-bs-theme="dark"] a:visited {
    color: var(--primary-color);
}

[data-bs-theme="dark"] a:hover {
    color: var(--primary-hover);
}

[data-bs-theme="dark"] .button,
[data-bs-theme="dark"] input[type=submit],
[data-bs-theme="dark"] input[type=button],
[data-bs-theme="dark"] .submit-row input {
    background: var(--primary-color);
    color: white;
}

[data-bs-theme="dark"] .button:hover,
[data-bs-theme="dark"] input[type=submit]:hover,
[data-bs-theme="dark"] input[type=button]:hover {
    background: var(--primary-hover);
}

[data-bs-theme="dark"] input[type=text],
[data-bs-theme="dark"] input[type=password],
[data-bs-theme="dark"] input[type=email],
[data-bs-theme="dark"] input[type=url],
[data-bs-theme="dark"] input[type=number],
[data-bs-theme="dark"] input[type=tel],
[data-bs-theme="dark"] textarea,
[data-bs-theme="dark"] select {
    background-color: var(--admin-input-bg);
    color: var(--admin-input-text);
    border-color: var(--admin-input-border);
}

[data-bs-theme="dark"] input[type=text]:focus,
[data-bs-theme="dark"] input[type=password]:focus,
[data-bs-theme="dark"] input[type=email]:focus,
[data-bs-theme="dark"] input[type=url]:focus,
[data-bs-theme="dark"] input[type=number]:focus,
[data-bs-theme="dark"] input[type=tel]:focus,
[data-bs-theme="dark"] textarea:focus,
[data-bs-theme="dark"] select:focus {
    border-color: var(--admin-input-focus-border);
}

/* Theme switcher styling */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--admin-border-color);
    padding: 8px;
    min-width: 180px;
}

[data-bs-theme="dark"] .dropdown-menu {
    background-color: #2d2d2d;
    border-color: #444;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(67, 97, 238, 0.1);
}

[data-bs-theme="dark"] .dropdown-item {
    color: #e9ecef;
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background-color: rgba(67, 97, 238, 0.2);
}

.dropdown-item.active {
    background-color: rgba(67, 97, 238, 0.2);
    color: var(--primary-color);
    font-weight: 500;
}

[data-bs-theme="dark"] .dropdown-item.active {
    background-color: rgba(67, 97, 238, 0.3);
    color: #4cc9f0;
}

/* Theme icons */
.dropdown-item i {
    width: 20px;
    text-align: center;
}

.dropdown-item:nth-child(1) i {
    color: #f9c74f;
}

.dropdown-item:nth-child(2) i {
    color: #4361ee;
}

.dropdown-item:nth-child(3) i {
    color: #7209b7;
}

/* Fix width issues in admin */
html, body {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

body {
    box-sizing: border-box;
}

#container {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    box-sizing: border-box;
}

#header {
    width: 100% !important;
    box-sizing: border-box;
}

#content {
    width: 100% !important;
    max-width: 100% !important;
    padding: 20px !important;
    box-sizing: border-box;
}

#content-main {
    width: 100% !important;
    float: none !important;
    box-sizing: border-box;
}

.colMS {
    margin: 0 !important;
    width: 100% !important;
    box-sizing: border-box;
}

.colM {
    width: 100% !important;
    box-sizing: border-box;
}

.module {
    width: 100% !important;
    box-sizing: border-box;
}

.admin-dashboard {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
}

/* Fix for dashboard grids */
.stats-grid,
.quick-actions-grid,
.activity-grid,
.app-grid {
    width: 100%;
    box-sizing: border-box;
}

/* Fix for changelist */
.changelist {
    width: 100% !important;
    box-sizing: border-box;
}

.changelist-form {
    width: 100% !important;
    box-sizing: border-box;
}

#changelist {
    width: 100% !important;
    box-sizing: border-box;
}

#changelist-filter {
    max-width: 240px !important;
    box-sizing: border-box;
}

#changelist-form .results {
    width: 100% !important;
    box-sizing: border-box;
}

.results table {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for forms */
.form-row {
    width: 100% !important;
    box-sizing: border-box;
}

fieldset {
    width: 100% !important;
    box-sizing: border-box;
}

.aligned label {
    width: 160px !important;
    box-sizing: border-box;
}

.aligned .fieldBox {
    width: 100% !important;
    box-sizing: border-box;
}

form .aligned p.help {
    margin-left: 160px !important;
    padding-left: 10px !important;
    box-sizing: border-box;
}

form .aligned div.help {
    margin-left: 160px !important;
    padding-left: 10px !important;
    box-sizing: border-box;
}

/* Fix for tables */
table {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for dashboard cards */
.dashboard-card {
    width: 100% !important;
    box-sizing: border-box;
}

.card-header, .card-body {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for app list */
.app-list {
    width: 100% !important;
    box-sizing: border-box;
}

.app-item {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for breadcrumbs */
.breadcrumbs {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for pagination */
.pagination {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for object tools */
.object-tools {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for submit row */
.submit-row {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for inline groups */
.inline-group {
    width: 100% !important;
    box-sizing: border-box;
}

.inline-related {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for tabular inline */
.tabular {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for stacked inline */
.stacked {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for dashboard stats */
.stat-card {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for quick actions */
.quick-action {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for activity items */
.activity-item {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for app models */
.app-models {
    width: 100% !important;
    box-sizing: border-box;
}

.app-model-item {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for selector */
.selector {
    width: 100% !important;
    box-sizing: border-box;
}

.selector-available, .selector-chosen {
    width: 45% !important;
    box-sizing: border-box;
}

/* Fix for date/time widgets */
.datetime {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for filter */
.filter {
    width: 100% !important;
    box-sizing: border-box;
}

/* Fix for dashboard section titles */
.section-title {
    width: 100% !important;
    box-sizing: border-box;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--admin-border-color);
}


