from django import forms
from .models import MaintenancePost, SpecialistApplication
from app_accounts.models import CustomUser
from django.utils import timezone

class MaintenancePostForm(forms.ModelForm):
    deadline = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        help_text="Select the deadline for this maintenance task",
        initial=timezone.now() + timezone.timedelta(days=7)
    )
    
    class Meta:
        model = MaintenancePost
        fields = ['title', 'description', 'category', 'experience', 'location', 'deadline', 
                  'requester_type', 'organization_name', 'organization_department']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter a descriptive title'}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Office Building, Floor 3, Room 302'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'experience': forms.Select(attrs={'class': 'form-select'}),
            'requester_type': forms.Select(attrs={'class': 'form-select'}),
            'organization_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Acme Corporation'}),
            'organization_department': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., IT Department, Finance, HR'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Make organization fields not required by default
        self.fields['organization_name'].required = False
        self.fields['organization_department'].required = False
        
        # Add JavaScript to toggle organization fields visibility
        self.fields['requester_type'].widget.attrs.update({
            'class': 'form-select',
            'onchange': 'toggleOrganizationFields(this.value)'
        })
        
        # Add Bootstrap classes to deadline field
        self.fields['deadline'].widget.attrs.update({'class': 'form-control'})
    
    def clean(self):
        cleaned_data = super().clean()
        requester_type = cleaned_data.get('requester_type')
        organization_name = cleaned_data.get('organization_name')
        deadline = cleaned_data.get('deadline')
        
        if requester_type == 'organization' and not organization_name:
            self.add_error('organization_name', 'Organization name is required when posting as an organization')
        
        # Validate deadline is in the future
        if deadline and deadline < timezone.now():
            self.add_error('deadline', 'Deadline must be in the future')
        
        return cleaned_data

class SpecialistApplicationForm(forms.ModelForm):
    class Meta:
        model = SpecialistApplication
        fields = ['message', 'cv_file', 'business_license']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Add help text and validation for file uploads
        self.fields['cv_file'].help_text = "Upload your CV/resume (PDF or DOC format, max 5MB)"
        self.fields['cv_file'].widget.attrs.update({'accept': '.pdf,.doc,.docx'})
        
        self.fields['business_license'].help_text = "Upload your business license if applying as a company (PDF format, max 5MB)"
        self.fields['business_license'].widget.attrs.update({'accept': '.pdf'})
        
        # Set required fields based on user type
        if self.user and hasattr(self.user, 'user_type'):
            if self.user.user_type == 'specialist':
                self.fields['cv_file'].required = True
                self.fields['business_license'].required = False
            # For future use if we add a company user type
            # elif self.user.user_type == 'company':
            #     self.fields['business_license'].required = True
    
    def clean_cv_file(self):
        cv_file = self.cleaned_data.get('cv_file')
        if cv_file:
            # Check file size (5MB limit)
            if cv_file.size > 5 * 1024 * 1024:
                raise forms.ValidationError("File size must be under 5MB")
            
            # Check file extension
            file_name = cv_file.name.lower()
            if not (file_name.endswith('.pdf') or file_name.endswith('.doc') or file_name.endswith('.docx')):
                raise forms.ValidationError("Only PDF, DOC, or DOCX files are allowed")
        return cv_file
    
    def clean_business_license(self):
        business_license = self.cleaned_data.get('business_license')
        if business_license:
            # Check file size (5MB limit)
            if business_license.size > 5 * 1024 * 1024:
                raise forms.ValidationError("File size must be under 5MB")
            
            # Check file extension
            if not business_license.name.lower().endswith('.pdf'):
                raise forms.ValidationError("Only PDF files are allowed")
        return business_license

class PostSearchForm(forms.Form):
    """Form for searching and filtering maintenance posts"""

    # Search query
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by title, description, or location...',
            'id': 'search-input'
        })
    )

    # Category filter - will be populated dynamically as multiple choice
    category = forms.MultipleChoiceField(
        choices=[],  # Will be set in __init__
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Get categories that have registered specialists
        specialist_categories = CustomUser.objects.filter(
            user_type='specialist',
            category__isnull=False
        ).values_list('category', flat=True).distinct()

        # Get specialist count for each category
        category_counts = {}
        for category_code in specialist_categories:
            count = CustomUser.objects.filter(
                user_type='specialist',
                category=category_code
            ).count()
            category_counts[category_code] = count

        # Build choices with specialist counts
        category_choices = []
        for category_code in specialist_categories:
            category_display = dict(CustomUser.CATEGORY_CHOICES).get(category_code, category_code)
            specialist_count = category_counts[category_code]
            choice_label = f"{category_display} ({specialist_count} specialist{'s' if specialist_count != 1 else ''})"
            category_choices.append((category_code, choice_label))

        # Sort by display name
        category_choices = sorted(category_choices, key=lambda x: x[1])

        self.fields['category'].choices = category_choices

    # Status filter - exclude certain statuses from search
    SEARCH_STATUS_CHOICES = [
        ('open', 'Open'),
        ('completed', 'Completed'),
        ('closed', 'Closed'),
    ]

    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + SEARCH_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Experience filter
    experience = forms.ChoiceField(
        choices=[('', 'Any Experience')] + list(MaintenancePost.EXPERIENCE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Requester type filter
    requester_type = forms.ChoiceField(
        choices=[('', 'All Types')] + list(MaintenancePost.REQUESTER_TYPE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Date range filters
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'placeholder': 'From date'
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date',
            'placeholder': 'To date'
        })
    )

    # Sort options
    SORT_CHOICES = [
        ('-created_at', 'Newest First'),
        ('created_at', 'Oldest First'),
        ('title', 'Title A-Z'),
        ('-title', 'Title Z-A'),
        ('deadline', 'Deadline (Earliest)'),
        ('-deadline', 'Deadline (Latest)'),
    ]

    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        initial='-created_at',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

class SpecialistSearchForm(forms.Form):
    """Form for searching and filtering specialists"""

    # Search query
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, username, or bio...',
        })
    )

    # Category filter
    category = forms.ChoiceField(
        choices=[('', 'All Categories')] + list(CustomUser.CATEGORY_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Rating filter
    RATING_CHOICES = [
        ('', 'Any Rating'),
        ('4', '4+ Stars'),
        ('3', '3+ Stars'),
        ('2', '2+ Stars'),
        ('1', '1+ Stars'),
    ]

    min_rating = forms.ChoiceField(
        choices=RATING_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Sort options
    SORT_CHOICES = [
        ('-date_joined', 'Newest Members'),
        ('date_joined', 'Oldest Members'),
        ('username', 'Name A-Z'),
        ('-username', 'Name Z-A'),
        ('rating', 'Highest Rated'),
        ('completed_jobs', 'Most Experienced'),
    ]

    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        initial='-date_joined',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

