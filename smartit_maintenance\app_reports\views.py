from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
import json
from collections import defaultdict

from app_posts.models import MaintenancePost, SpecialistApplication, SpecialistRating
from app_accounts.models import CustomUser
from app_notifications.models import Notification

@login_required
def dashboard_analytics(request):
    """Main analytics dashboard"""
    # Only allow admin users to access analytics
    if not hasattr(request.user, 'user_type') or request.user.user_type != 'admin':
        return render(request, 'app_reports/access_denied.html')

    # Get date range (default to last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # Override with user-provided dates if available
    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()

    # Basic statistics
    stats = {
        'total_posts': MaintenancePost.objects.count(),
        'total_users': CustomUser.objects.filter(user_type='user').count(),
        'total_specialists': CustomUser.objects.filter(user_type='specialist').count(),
        'total_applications': SpecialistApplication.objects.count(),
        'completed_jobs': MaintenancePost.objects.filter(status='completed').count(),
        'active_posts': MaintenancePost.objects.filter(status__in=['open', 'in_progress']).count(),
    }

    # Posts by status
    posts_by_status = MaintenancePost.objects.values('status').annotate(count=Count('id'))

    # Posts by category
    posts_by_category = MaintenancePost.objects.values('category').annotate(count=Count('id'))

    # Recent activity (last 7 days)
    recent_date = timezone.now().date() - timedelta(days=7)
    recent_posts = MaintenancePost.objects.filter(created_at__date__gte=recent_date).count()
    recent_applications = SpecialistApplication.objects.filter(application_date__date__gte=recent_date).count()
    recent_users = CustomUser.objects.filter(date_joined__date__gte=recent_date).count()

    # Top specialists by rating
    top_specialists = []
    specialists = CustomUser.objects.filter(user_type='specialist')
    for specialist in specialists:
        avg_rating = specialist.get_average_rating()
        if avg_rating:
            top_specialists.append({
                'specialist': specialist,
                'rating': avg_rating,
                'total_ratings': specialist.get_total_ratings_count(),
                'completed_jobs': specialist.get_completed_jobs_count()
            })
    top_specialists.sort(key=lambda x: x['rating'], reverse=True)
    top_specialists = top_specialists[:10]

    # Monthly trends (last 12 months)
    monthly_data = []
    for i in range(12):
        month_start = (timezone.now().replace(day=1) - timedelta(days=30*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        posts_count = MaintenancePost.objects.filter(
            created_at__date__gte=month_start,
            created_at__date__lte=month_end
        ).count()

        applications_count = SpecialistApplication.objects.filter(
            application_date__date__gte=month_start,
            application_date__date__lte=month_end
        ).count()

        monthly_data.append({
            'month': month_start.strftime('%b %Y'),
            'posts': posts_count,
            'applications': applications_count
        })

    monthly_data.reverse()

    context = {
        'stats': stats,
        'posts_by_status': list(posts_by_status),
        'posts_by_category': list(posts_by_category),
        'recent_posts': recent_posts,
        'recent_applications': recent_applications,
        'recent_users': recent_users,
        'top_specialists': top_specialists,
        'monthly_data': monthly_data,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'app_reports/dashboard_analytics.html', context)

@login_required
def export_data(request):
    """Export data to CSV format"""
    if not hasattr(request.user, 'user_type') or request.user.user_type != 'admin':
        return HttpResponse('Access denied', status=403)

    import csv
    from django.http import HttpResponse

    export_type = request.GET.get('type', 'posts')

    response = HttpResponse(content_type='text/csv')

    if export_type == 'posts':
        response['Content-Disposition'] = 'attachment; filename="maintenance_posts.csv"'
        writer = csv.writer(response)
        writer.writerow(['ID', 'Title', 'Category', 'Status', 'Created By', 'Created Date', 'Location', 'Experience Required'])

        posts = MaintenancePost.objects.all().select_related('created_by')
        for post in posts:
            writer.writerow([
                post.id,
                post.title,
                post.get_category_display(),
                post.get_status_display(),
                post.created_by.username,
                post.created_at.strftime('%Y-%m-%d %H:%M'),
                post.location or '',
                post.get_experience_display()
            ])

    elif export_type == 'specialists':
        response['Content-Disposition'] = 'attachment; filename="specialists.csv"'
        writer = csv.writer(response)
        writer.writerow(['Username', 'Name', 'Email', 'Category', 'Average Rating', 'Total Ratings', 'Completed Jobs', 'Join Date'])

        specialists = CustomUser.objects.filter(user_type='specialist')
        for specialist in specialists:
            writer.writerow([
                specialist.username,
                specialist.get_full_name(),
                specialist.email,
                specialist.get_category_display() if specialist.category else '',
                specialist.get_average_rating() or 0,
                specialist.get_total_ratings_count(),
                specialist.get_completed_jobs_count(),
                specialist.date_joined.strftime('%Y-%m-%d')
            ])

    elif export_type == 'applications':
        response['Content-Disposition'] = 'attachment; filename="applications.csv"'
        writer = csv.writer(response)
        writer.writerow(['ID', 'Post Title', 'Specialist', 'Status', 'Application Date', 'Message'])

        applications = SpecialistApplication.objects.all().select_related('post', 'specialist')
        for app in applications:
            writer.writerow([
                app.id,
                app.post.title,
                app.specialist.username,
                app.get_status_display(),
                app.application_date.strftime('%Y-%m-%d %H:%M'),
                app.message[:100] + '...' if len(app.message) > 100 else app.message
            ])

    return response

@login_required
def generate_report(request):
    """Generate detailed PDF report"""
    if not hasattr(request.user, 'user_type') or request.user.user_type != 'admin':
        return HttpResponse('Access denied', status=403)

    # For now, return a simple HTML report that can be printed as PDF
    # In a production environment, you might want to use libraries like ReportLab or WeasyPrint

    # Get date range
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()

    # Gather comprehensive data
    posts_in_period = MaintenancePost.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    applications_in_period = SpecialistApplication.objects.filter(
        application_date__date__gte=start_date,
        application_date__date__lte=end_date
    )

    # Statistics
    report_data = {
        'start_date': start_date,
        'end_date': end_date,
        'posts_created': posts_in_period.count(),
        'applications_submitted': applications_in_period.count(),
        'posts_completed': posts_in_period.filter(status='completed').count(),
        'posts_by_category': posts_in_period.values('category').annotate(count=Count('id')),
        'posts_by_status': posts_in_period.values('status').annotate(count=Count('id')),
        'top_categories': posts_in_period.values('category').annotate(count=Count('id')).order_by('-count')[:5],
        'completion_rate': (posts_in_period.filter(status='completed').count() / posts_in_period.count() * 100) if posts_in_period.count() > 0 else 0,
    }

    context = {
        'report_data': report_data,
        'posts_in_period': posts_in_period[:20],  # Show top 20 posts
        'applications_in_period': applications_in_period[:20],  # Show top 20 applications
    }

    return render(request, 'app_reports/detailed_report.html', context)

@login_required
def api_chart_data(request):
    """API endpoint for chart data"""
    if not hasattr(request.user, 'user_type') or request.user.user_type != 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)

    chart_type = request.GET.get('type', 'posts_by_month')

    if chart_type == 'posts_by_month':
        # Last 12 months data
        data = []
        labels = []

        for i in range(12):
            month_start = (timezone.now().replace(day=1) - timedelta(days=30*i)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            count = MaintenancePost.objects.filter(
                created_at__date__gte=month_start,
                created_at__date__lte=month_end
            ).count()

            data.append(count)
            labels.append(month_start.strftime('%b %Y'))

        data.reverse()
        labels.reverse()

        return JsonResponse({
            'labels': labels,
            'data': data,
            'title': 'Posts Created by Month'
        })

    elif chart_type == 'posts_by_category':
        posts_by_category = MaintenancePost.objects.values('category').annotate(count=Count('id'))

        labels = []
        data = []

        for item in posts_by_category:
            # Convert category code to display name
            category_display = dict(CustomUser.CATEGORY_CHOICES).get(item['category'], item['category'])
            labels.append(category_display)
            data.append(item['count'])

        return JsonResponse({
            'labels': labels,
            'data': data,
            'title': 'Posts by Category'
        })

    elif chart_type == 'posts_by_status':
        posts_by_status = MaintenancePost.objects.values('status').annotate(count=Count('id'))

        labels = []
        data = []
        colors = {
            'open': '#28a745',
            'in_progress': '#007bff',
            'completed': '#17a2b8',
            'closed': '#6c757d'
        }
        background_colors = []

        for item in posts_by_status:
            status_display = dict(MaintenancePost.STATUS_CHOICES).get(item['status'], item['status'])
            labels.append(status_display)
            data.append(item['count'])
            background_colors.append(colors.get(item['status'], '#6c757d'))

        return JsonResponse({
            'labels': labels,
            'data': data,
            'backgroundColor': background_colors,
            'title': 'Posts by Status'
        })

    return JsonResponse({'error': 'Invalid chart type'}, status=400)
