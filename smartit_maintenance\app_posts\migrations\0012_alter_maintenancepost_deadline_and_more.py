# Generated by Django 5.2.1 on 2025-06-03 04:05

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_posts', '0011_alter_maintenancepost_deadline_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='maintenancepost',
            name='deadline',
            field=models.DateTimeField(default=datetime.datetime(2025, 6, 10, 4, 5, 38, 158932, tzinfo=datetime.timezone.utc)),
        ),
        migrations.AlterField(
            model_name='maintenancepost',
            name='status',
            field=models.CharField(choices=[('open', 'Open'), ('pending_confirmation', 'Pending Confirmation'), ('in_progress', 'In Progress'), ('pending_approval', 'Pending Approval'), ('completed', 'Completed'), ('closed', 'Closed')], default='open', max_length=20),
        ),
    ]
