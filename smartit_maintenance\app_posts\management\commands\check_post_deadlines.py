from django.core.management.base import BaseCommand
from django.utils import timezone
from app_posts.models import MaintenancePost

class Command(BaseCommand):
    help = 'Check and display information about post deadlines'

    def add_arguments(self, parser):
        parser.add_argument(
            '--expired-only',
            action='store_true',
            help='Show only expired posts',
        )
        parser.add_argument(
            '--close-expired',
            action='store_true',
            help='Automatically close expired posts',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        
        if options['expired_only']:
            posts = MaintenancePost.objects.filter(
                status__in=['open', 'pending_confirmation', 'in_progress', 'pending_approval'],
                deadline__lt=now
            )
            self.stdout.write(f"\n📅 Expired Posts ({posts.count()}):")
        else:
            posts = MaintenancePost.objects.filter(
                status__in=['open', 'pending_confirmation', 'in_progress', 'pending_approval']
            )
            self.stdout.write(f"\n📅 Active Posts ({posts.count()}):")
        
        if not posts.exists():
            self.stdout.write(self.style.SUCCESS("✅ No posts found"))
            return
        
        for post in posts:
            expired = post.deadline < now
            time_diff = abs((post.deadline - now).total_seconds())
            
            if time_diff < 3600:  # Less than 1 hour
                time_str = f"{int(time_diff // 60)} minutes"
            elif time_diff < 86400:  # Less than 1 day
                time_str = f"{int(time_diff // 3600)} hours"
            else:
                time_str = f"{int(time_diff // 86400)} days"
            
            status_icon = "🔴" if expired else "🟢"
            time_prefix = "overdue by" if expired else "expires in"
            
            self.stdout.write(
                f"{status_icon} {post.title[:50]}... "
                f"({post.status}) - {time_prefix} {time_str}"
            )
        
        if options['close_expired']:
            from app_posts.utils import close_expired_posts
            count = close_expired_posts()
            self.stdout.write(
                self.style.SUCCESS(f"\n✅ Closed {count} expired posts")
            )
