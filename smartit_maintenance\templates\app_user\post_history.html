{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Request History</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='post_history' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Your Maintenance Request History</h4>
                        {% if page_obj.paginator.count > 0 %}
                        <small class="text-light">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} requests
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if posts %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for post in posts %}
                                        <tr>
                                            <td>{{ post.title }}</td>
                                            <td><span class="badge bg-info">{{ post.get_category_display }}</span></td>
                                            <td><span class="badge bg-{% if post.status == 'open' %}warning{% elif post.status == 'in_progress' %}primary{% elif post.status == 'completed' %}success{% else %}secondary{% endif %}">
                                                {{ post.get_status_display }}
                                            </span></td>
                                            <td>{{ post.created_at|date:"M d, Y" }}</td>
                                            <td>
                                                <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Post history pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}

                    {% else %}
                        <p>You haven't created any maintenance posts yet.</p>
                        <a href="{% url 'create_post' %}" class="btn btn-primary">Create Your First Post</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}