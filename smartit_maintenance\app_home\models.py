from django.db import models
from app_accounts.models import CustomUser
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth import get_user_model

class SupportMessage(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='support_messages')
    subject = models.CharField(max_length=255)
    message = models.TextField()
    admin_reply = models.TextField(blank=True, null=True)
    replied_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_resolved = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'support_messages'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.subject} - {self.user.username}"
    
    def send_reply_notification(self):
        """Send email notification to user when admin replies"""
        if self.admin_reply and self.user.email:
            subject = f"Reply to your support request: {self.subject}"
            message = f"""
Hello {self.user.username},

An administrator has replied to your support request:

Your request: {self.message}

Admin reply: {self.admin_reply}

Please log in to your account to view the full conversation.

Thank you,
Smart-IT Maintenance Team
            """
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [self.user.email],
                fail_silently=False,
            )
    
    def notify_admins(self):
        """Notify all admin users about a new support message"""
        try:
            from app_notifications.utils import create_notification
            
            # Get all admin users
            User = get_user_model()
            admin_users = User.objects.filter(user_type='admin')
            
            # Create a notification for each admin
            for admin in admin_users:
                create_notification(
                    recipient=admin,
                    notification_type='new_support_message',
                    title='New Support Message',
                    message=f'{self.user.username} has submitted a new support request: "{self.subject}"',
                )
                
            # If SMS notifications are enabled, send SMS to admins with phone numbers
            if hasattr(settings, 'SMS_NOTIFICATIONS_ENABLED') and settings.SMS_NOTIFICATIONS_ENABLED:
                from app_accounts.sms import send_sms_notification
                for admin in admin_users:
                    if admin.phone_number:
                        message = f"New support request from {self.user.username}: {self.subject}"
                        send_sms_notification(admin, admin.phone_number, message)
                        
        except ImportError:
            # If notification app is not available, just continue
            pass
