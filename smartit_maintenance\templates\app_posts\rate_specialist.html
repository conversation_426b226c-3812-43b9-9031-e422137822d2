{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Rate Specialist</h4>
                </div>
                <div class="card-body">
                    <h5 class="mb-3">Rate {{ specialist.get_full_name|default:specialist.username }} for "{{ post.title }}"</h5>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label class="form-label">Rating:</label>
                            <div class="rating-input mb-2">
                                <div class="btn-group" role="group">
                                    {% for i in "12345" %}
                                    <input type="radio" class="btn-check" name="rating" id="rating{{ forloop.counter }}" value="{{ forloop.counter }}" {% if forloop.counter == 5 %}checked{% endif %}>
                                    <label class="btn btn-outline-warning" for="rating{{ forloop.counter }}">
                                        {{ forloop.counter }} <i class="fas fa-star"></i>
                                    </label>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="form-text">
                                1 = Poor, 2 = Fair, 3 = Good, 4 = Very Good, 5 = Excellent
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="comment" class="form-label">Comment (Optional):</label>
                            <textarea name="comment" id="comment" class="form-control" rows="4" placeholder="Share your experience with this specialist..."></textarea>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'post_detail' post_id=post.id %}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Submit Rating</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Highlight stars on hover
    document.addEventListener('DOMContentLoaded', function() {
        const ratingLabels = document.querySelectorAll('.rating-input label');
        
        ratingLabels.forEach((label, index) => {
            label.addEventListener('mouseover', function() {
                // Highlight all stars up to this one
                for (let i = 0; i <= index; i++) {
                    ratingLabels[i].classList.add('active');
                }
                
                // Remove highlight from stars after this one
                for (let i = index + 1; i < ratingLabels.length; i++) {
                    ratingLabels[i].classList.remove('active');
                }
            });
        });
        
        // Remove highlight when mouse leaves the rating container
        document.querySelector('.rating-input').addEventListener('mouseleave', function() {
            ratingLabels.forEach(label => {
                label.classList.remove('active');
            });
        });
    });
</script>
{% endblock %}