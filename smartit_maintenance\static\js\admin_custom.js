// Make sure the logout functionality is working correctly
document.addEventListener('DOMContentLoaded', function() {
    // Find all links to the logout URL and convert them to POST form submissions
    const logoutLinks = document.querySelectorAll('a[href*="/admin/logout/"]');
    
    logoutLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create a form element
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = this.href;
            form.style.display = 'none';
            
            // Add CSRF token
            const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'csrfmiddlewaretoken';
            input.value = csrfToken;
            form.appendChild(input);
            
            // Append form to body and submit
            document.body.appendChild(form);
            form.submit();
        });
    });
    
    // Also ensure the logout form in the dropdown works
    const logoutForm = document.getElementById('logout-form');
    if (logoutForm) {
        logoutForm.addEventListener('submit', function(e) {
            // No need to prevent default as we want the form to submit
            console.log('Logout form submitted');
        });
    }
});

// Theme switcher functionality
document.addEventListener('DOMContentLoaded', function() {
    'use strict'

    const getStoredTheme = () => localStorage.getItem('theme')
    const setStoredTheme = theme => localStorage.setItem('theme', theme)

    const getPreferredTheme = () => {
        const storedTheme = getStoredTheme()
        if (storedTheme) {
            return storedTheme
        }

        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }

    const setTheme = theme => {
        if (theme === 'auto') {
            document.documentElement.setAttribute('data-bs-theme', 
                window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
            )
        } else {
            document.documentElement.setAttribute('data-bs-theme', theme)
        }
        
        // Update the icon
        updateThemeIcon(theme)
    }

    const updateThemeIcon = (theme) => {
        const themeIcon = document.getElementById('theme-icon-admin')
        if (themeIcon) {
            // Remove all icon classes
            themeIcon.classList.remove('fa-sun', 'fa-moon', 'fa-circle-half-stroke')
            
            // Add appropriate icon
            if (theme === 'dark' || 
               (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                themeIcon.classList.add('fa-moon')
            } else if (theme === 'auto') {
                themeIcon.classList.add('fa-circle-half-stroke')
            } else {
                themeIcon.classList.add('fa-sun')
            }
        }
        
        // Update active state in dropdown
        document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
            element.classList.remove('active')
            element.setAttribute('aria-pressed', 'false')
            
            if (element.getAttribute('data-bs-theme-value') === theme) {
                element.classList.add('active')
                element.setAttribute('aria-pressed', 'true')
            }
        })
    }

    // Set initial theme
    setTheme(getPreferredTheme())

    // Add event listeners to theme buttons
    document.querySelectorAll('[data-bs-theme-value]').forEach(toggle => {
        toggle.addEventListener('click', () => {
            const theme = toggle.getAttribute('data-bs-theme-value')
            setStoredTheme(theme)
            setTheme(theme)
        })
    })
    
    // Listen for OS theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        const storedTheme = getStoredTheme()
        if (storedTheme === 'auto') {
            setTheme('auto')
        }
    })
});

