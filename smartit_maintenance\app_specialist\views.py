from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from app_posts.models import MaintenancePost, SpecialistApplication

@login_required
def specialist_dashboard(request):
    """Dashboard view for specialists"""
    # Ensure the user is a specialist
    if request.user.user_type != 'specialist':
        return redirect('home')
    
    # Get counts for dashboard stats
    applications_count = SpecialistApplication.objects.filter(specialist=request.user).count()
    active_jobs_count = SpecialistApplication.objects.filter(
        specialist=request.user,
        status='confirmed',
        post__status='in_progress',
        post__assigned_to=request.user
    ).count()
    completed_jobs_count = SpecialistApplication.objects.filter(
        specialist=request.user,
        status='confirmed',
        post__status='completed',
        post__assigned_to=request.user
    ).count()
    
    # Get recent applications
    recent_applications = SpecialistApplication.objects.filter(
        specialist=request.user
    ).order_by('-application_date')[:5]
    
    # Get available posts matching specialist's category with pagination
    available_posts_list = MaintenancePost.objects.filter(
        status='open',
        category=request.user.category
    ).order_by('-created_at')

    # Set up pagination - show 6 posts per page
    paginator = Paginator(available_posts_list, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'applications_count': applications_count,
        'active_jobs_count': active_jobs_count,
        'completed_jobs_count': completed_jobs_count,
        'recent_applications': recent_applications,
        'available_posts': page_obj,
        'page_obj': page_obj,
        'total_available_posts': available_posts_list.count(),
    }
    return render(request, 'app_specialist/specialist_dashboard.html', context)

@login_required
def my_applications(request):
    """View for specialists to see their applications"""
    # Ensure the user is a specialist
    if request.user.user_type != 'specialist':
        return redirect('home')
    
    applications = SpecialistApplication.objects.filter(
        specialist=request.user
    ).order_by('-application_date')
    
    context = {
        'applications': applications,
    }
    return render(request, 'app_specialist/my_applications.html', context)

@login_required
def active_jobs(request):
    """View for specialists to see their active jobs"""
    # Ensure the user is a specialist
    if request.user.user_type != 'specialist':
        return redirect('home')
    
    active_jobs = SpecialistApplication.objects.filter(
        specialist=request.user,
        status='confirmed',
        post__status='in_progress',
        post__assigned_to=request.user
    ).order_by('-application_date')
    
    context = {
        'active_jobs': active_jobs,
    }
    return render(request, 'app_specialist/active_jobs.html', context)

@login_required
def maintenance_history(request):
    """View for specialists to see their maintenance history"""
    # Ensure the user is a specialist
    if request.user.user_type != 'specialist':
        return redirect('home')

    # Get completed maintenance jobs where:
    # 1. The post is completed
    # 2. The post is assigned to the current specialist
    # 3. The specialist has a confirmed application for the post
    completed_jobs = SpecialistApplication.objects.filter(
        specialist=request.user,
        status='confirmed',
        post__status='completed',
        post__assigned_to=request.user
    ).select_related('post', 'post__created_by').order_by('-post__created_at')

    # Get ratings for these completed jobs and calculate statistics
    from app_posts.models import SpecialistRating
    total_rating = 0
    rated_jobs_count = 0

    for job in completed_jobs:
        # Get the rating for this specific job
        rating = SpecialistRating.objects.filter(
            specialist=request.user,
            post=job.post
        ).first()
        job.rating = rating.rating if rating else None
        job.rating_comment = rating.comment if rating else None
        job.completion_date = job.post.created_at  # Use post creation date as completion reference

        # Calculate statistics
        if job.rating:
            total_rating += job.rating
            rated_jobs_count += 1

    # Calculate average rating
    average_rating = round(total_rating / rated_jobs_count, 1) if rated_jobs_count > 0 else 0

    # Get total count before pagination
    total_completed = completed_jobs.count()

    # Pagination - show 6 completed jobs per page
    paginator = Paginator(completed_jobs, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Recalculate ratings for paginated jobs only (for efficiency)
    for job in page_obj:
        # Get the rating for this specific job
        rating = SpecialistRating.objects.filter(
            specialist=request.user,
            post=job.post
        ).first()
        job.rating = rating.rating if rating else None
        job.rating_comment = rating.comment if rating else None
        job.completion_date = job.post.created_at  # Use post creation date as completion reference

    context = {
        'completed_jobs': page_obj,
        'page_obj': page_obj,
        'total_completed': total_completed,
        'rated_jobs_count': rated_jobs_count,
        'average_rating': average_rating,
    }
    return render(request, 'app_specialist/maintenance_history.html', context)



