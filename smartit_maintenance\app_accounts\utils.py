try:
    import requests
except ImportError:
    # Fallback implementation without requests
    def send_sms_verification(phone_number, message):
        """
        Fallback implementation when requests library is not available
        """
        print(f"SMS would be sent to {phone_number}: {message}")
        # Log this to a file or database
        return {"status": "error", "message": "requests library not installed"}
else:
    import json
    from django.conf import settings

    def send_sms_verification(phone_number, message):
        """
        Send SMS verification using an external API
        
        Args:
            phone_number (str): The phone number to send the SMS to
            message (str): The message to send
        
        Returns:
            dict: API response
        """
        try:
            # Get API settings from Django settings
            api_key = getattr(settings, 'API_KEY', '')
            secret_key = getattr(settings, 'SECRET_KEY_BEEM', '')
            sender_id = getattr(settings, 'SENDER_ID', 'SIMA Portal')
            
            # Check if SMS debug mode is enabled
            sms_debug_mode = getattr(settings, 'SMS_DEBUG_MODE', False)
            
            if sms_debug_mode:
                print(f"SMS would be sent to {phone_number}: {message}")
                return {"status": "success", "message": "SMS sent successfully (debug mode)"}
            
            # Beem Africa API endpoint
            url = "https://apisms.beem.africa/v1/send"
            
            # Prepare headers and payload
            import base64
            auth_string = f"{api_key}:{secret_key}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Basic {auth_b64}'
            }
            
            payload = json.dumps({
                "source_addr": sender_id,
                "schedule_time": "",
                "encoding": "0",
                "message": message,
                "recipients": [{"recipient_id": "1", "dest_addr": phone_number}]
            })
            
            # Log what we're about to send for debugging
            print(f"Sending SMS to {phone_number} with API key: {api_key}")
            print(f"Headers: {headers}")
            print(f"Payload: {payload}")
            
            # Make the API request
            response = requests.post(url, headers=headers, data=payload)
            
            # Log the response for debugging
            print(f"SMS API Response: {response.status_code} - {response.text}")
            
            if response.status_code == 200:
                return {"status": "success", "message": "SMS sent successfully", "data": response.json()}
            else:
                return {"status": "error", "message": f"SMS API error: {response.status_code}", "data": response.text}
        except Exception as e:
            # Log the error and return a failure response
            print(f"SMS API Error: {str(e)}")
            return {"status": "error", "message": str(e)}











