/* Dark Mode Comprehensive Fixes */

/* Global dark mode improvements */
[data-bs-theme="dark"] {
    /* Ensure proper text contrast */
    color-scheme: dark;
}

/* Fix notification dropdown in dark mode */
[data-bs-theme="dark"] .dropdown-item.bg-light {
    background-color: var(--bs-tertiary-bg) !important;
    color: var(--bs-body-color) !important;
}

[data-bs-theme="dark"] .dropdown-item.bg-light:hover {
    background-color: var(--bs-secondary-bg) !important;
}

/* Status badges with better contrast */
[data-bs-theme="dark"] .badge.bg-success {
    background-color: #28a745 !important;
    color: #fff !important;
}

[data-bs-theme="dark"] .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

[data-bs-theme="dark"] .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #fff !important;
}

[data-bs-theme="dark"] .badge.bg-info {
    background-color: #17a2b8 !important;
    color: #fff !important;
}

[data-bs-theme="dark"] .badge.bg-primary {
    background-color: #007bff !important;
    color: #fff !important;
}

[data-bs-theme="dark"] .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
}

/* Table improvements */
[data-bs-theme="dark"] .table th {
    color: var(--bs-emphasis-color);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .table td {
    color: var(--bs-body-color);
    border-color: var(--bs-border-color);
}

/* Button improvements */
[data-bs-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: #000;
}

[data-bs-theme="dark"] .btn-outline-secondary {
    color: var(--bs-secondary-color);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--bs-secondary-color);
    color: #000;
}

/* Card header improvements */
[data-bs-theme="dark"] .card-header.bg-primary {
    background-color: var(--primary-color) !important;
    color: #000 !important;
}

[data-bs-theme="dark"] .card-header.bg-light {
    background-color: var(--bs-tertiary-bg) !important;
    color: var(--bs-emphasis-color) !important;
}

/* Text color utilities for dark mode */
[data-bs-theme="dark"] .text-body-secondary {
    color: var(--bs-secondary-color) !important;
}

[data-bs-theme="dark"] .text-body-emphasis {
    color: var(--bs-emphasis-color) !important;
}

/* Link improvements */
[data-bs-theme="dark"] a {
    color: var(--primary-color);
}

[data-bs-theme="dark"] a:hover {
    color: var(--info-color);
}

/* Progress bars */
[data-bs-theme="dark"] .progress {
    background-color: var(--bs-tertiary-bg);
}

/* Breadcrumb */
[data-bs-theme="dark"] .breadcrumb {
    background-color: var(--bs-secondary-bg);
}

[data-bs-theme="dark"] .breadcrumb-item + .breadcrumb-item::before {
    color: var(--bs-secondary-color);
}

/* Accordion */
[data-bs-theme="dark"] .accordion-item {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .accordion-button {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .accordion-button:not(.collapsed) {
    background-color: var(--bs-tertiary-bg);
    color: var(--bs-emphasis-color);
}

/* Offcanvas */
[data-bs-theme="dark"] .offcanvas {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .offcanvas-header {
    border-color: var(--bs-border-color);
}

/* Toast */
[data-bs-theme="dark"] .toast {
    background-color: var(--bs-secondary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .toast-header {
    background-color: var(--bs-tertiary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-emphasis-color);
}

/* Navbar brand improvements */
[data-bs-theme="dark"] .navbar-brand {
    color: var(--bs-emphasis-color) !important;
}

/* Input group */
[data-bs-theme="dark"] .input-group-text {
    background-color: var(--bs-tertiary-bg);
    border-color: var(--bs-border-color);
    color: var(--bs-body-color);
}

/* Spinner */
[data-bs-theme="dark"] .spinner-border {
    color: var(--primary-color);
}

/* Close button */
[data-bs-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Custom scrollbar for dark mode */
[data-bs-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bs-secondary-bg);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bs-border-color);
    border-radius: 4px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--bs-secondary-color);
}

/* Selection color */
[data-bs-theme="dark"] ::selection {
    background-color: var(--primary-color);
    color: #000;
}

/* Focus outline improvements */
[data-bs-theme="dark"] *:focus {
    outline-color: var(--primary-color);
}

/* Ensure all text is visible */
[data-bs-theme="dark"] * {
    color: inherit;
}

[data-bs-theme="dark"] .text-white {
    color: #ffffff !important;
}

[data-bs-theme="dark"] .text-black {
    color: #000000 !important;
}

/* Fix for any remaining invisible text */
[data-bs-theme="dark"] .invisible-text-fix {
    color: var(--bs-body-color) !important;
}
