from django.urls import path
from . import views

urlpatterns = [
    path('create/', views.CreateMaintenancePostView.as_view(), name='create_post'),
    path('search/', views.search_posts, name='search_posts'),
    path('specialists/search/', views.search_specialists, name='search_specialists'),
    path('<int:post_id>/', views.post_detail, name='post_detail'),
    path('<int:post_id>/apply/', views.apply_for_post, name='apply_for_post'),
    path('<int:post_id>/edit/', views.edit_post, name='edit_post'),
    path('<int:post_id>/close/', views.close_post, name='close_post'),
    path('application/<int:application_id>/', views.application_detail, name='application_detail'),
    path('application/<int:application_id>/accept/', views.accept_application, name='accept_application'),
    path('application/<int:application_id>/reject/', views.reject_application, name='reject_application'),
    path('<int:post_id>/application/<int:application_id>/confirm/', views.confirm_application_with_post, name='confirm_job_with_post'),
    path('application/<int:application_id>/confirm/', views.confirm_application, name='confirm_job'),
    path('application/<int:application_id>/decline/', views.decline_job, name='decline_job'),
    path('<int:post_id>/complete/', views.complete_job, name='mark_job_complete'),
    path('<int:post_id>/approve/', views.approve_job, name='approve_job'),
    path('<int:post_id>/reject/', views.reject_job, name='reject_job'),
    path('application/<int:application_id>/rate/', views.rate_specialist, name='rate_specialist'),
]


