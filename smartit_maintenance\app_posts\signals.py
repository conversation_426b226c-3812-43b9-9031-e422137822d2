from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import MaintenancePost
from app_notifications.utils import create_notification
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=MaintenancePost)
def check_post_deadline(sender, instance, **kwargs):
    """
    Signal to automatically close posts when they are accessed and found to be expired
    """
    # Only check if the post is not already closed or completed
    if instance.status in ['open', 'pending_confirmation', 'in_progress', 'pending_approval']:
        if instance.deadline < timezone.now():
            # Post has expired, close it
            old_status = instance.status
            instance.status = 'closed'
            
            logger.info(f"Auto-closing expired post: {instance.title} (was: {old_status})")
            
            # Note: We can't send notifications here because the instance hasn't been saved yet
            # The notification will be sent by the post_save signal or by the periodic task
