from django.contrib import admin
from django.http import Http404
from app_accounts.models import CustomUser
from app_posts.models import MaintenancePost
from app_home.models import SupportMessage

# Custom admin site with additional context
class SmartITAdminSite(admin.AdminSite):
    site_header = 'Smart-IT Maintenance Administration'
    site_title = 'Smart-IT Admin'
    index_title = 'Maintenance Management Dashboard'
    
    def each_context(self, request):
        context = super().each_context(request)
        context['extra_css'] = ['css/admin_custom.css']
        
        # Add counts for dashboard stats
        if request.path == '/admin/':
            context['total_users'] = CustomUser.objects.count()
            context['total_posts'] = MaintenancePost.objects.count()
            context['total_support'] = SupportMessage.objects.count()
            
            # Add counts for recent activity
            context['recent_users'] = CustomUser.objects.order_by('-date_joined')[:5]
            context['recent_posts'] = MaintenancePost.objects.order_by('-created_at')[:5]
            context['recent_support'] = SupportMessage.objects.order_by('-created_at')[:5]
        
        return context
    
    # Customize the app index to group models better
    def app_index(self, request, app_label, extra_context=None):
        app_dict = self._build_app_dict(request, app_label)
        
        # Sort models alphabetically within each app
        for app in app_dict.values():
            app['models'].sort(key=lambda x: x['name'])
        
        if not app_dict:
            raise Http404('The requested admin page does not exist.')
        
        context = {
            **self.each_context(request),
            'title': f'{app_label.title()} administration',
            'app_list': [app_dict],
            'app_label': app_label,
            **(extra_context or {}),
        }
        
        return super().app_index(request, app_label, extra_context=context)

    # Override get_app_list to customize the order
    def get_app_list(self, request, app_label=None):
        app_dict = self._build_app_dict(request, app_label)
        app_list = sorted(app_dict.values(), key=lambda x: APP_ORDER.get(x['app_label'], 999))

        # Sort models within each app
        for app in app_list:
            app['models'].sort(key=lambda x: x['name'])

        return app_list

# Initialize the custom admin site
admin_site = SmartITAdminSite(name='smartit_admin')

# Add app order and icons
APP_ORDER = {
    'app_accounts': 1,
    'app_posts': 2,
    'app_specialist': 3,
    'app_user': 4,
    'app_home': 5,
    'app_notifications': 6,
    'auth': 7,
    'admin': 8,
}


