{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card mt-5 mb-5">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Create an Account</h3>
                </div>
                <div class="card-body">
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <form method="post" id="registerForm" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <h5 class="border-bottom pb-2 mb-4">Account Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">Username*</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email Address*</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Password Section -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">Password*</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password1.errors }}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Your password must contain at least 8 characters and can't be entirely numeric.
                                </small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password*</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password2.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- User Type and Category -->
                        <h5 class="border-bottom pb-2 mb-4 mt-4">Role Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.user_type.id_for_label }}" class="form-label">User Type*</label>
                                {{ form.user_type }}
                                {% if form.user_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.user_type.errors }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3" id="category-field">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Category*</label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.category.errors }}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">Required for users and specialists</small>
                            </div>
                        </div>
                        
                        <!-- Contact Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number*</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone_number.errors }}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">Enter your phone number with Tanzania country code (must be exactly 13 characters, e.g., +************). Do not start with 0.</small>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">Register</button>
                            <a href="{% url 'login' %}" class="btn btn-outline-secondary ms-2">Already have an account?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const userTypeSelect = document.getElementById('{{ form.user_type.id_for_label }}');
        const categoryField = document.getElementById('category-field');
        const categorySelect = document.getElementById('{{ form.category.id_for_label }}');
        
        function toggleCategoryField() {
            const selectedValue = userTypeSelect.value;
            if (selectedValue === 'specialist') {
                // For specialists, show and require the category
                categoryField.style.display = 'block';
                categorySelect.required = true;
                categorySelect.setAttribute('aria-required', 'true');
                document.querySelector('small.form-text').textContent = 'Required for specialists';
            } else {
                // For regular users, hide the category field
                categoryField.style.display = 'none';
                categorySelect.required = false;
                categorySelect.removeAttribute('aria-required');
            }
        }
        
        // Initial check
        toggleCategoryField();
        
        // Add event listener for changes
        userTypeSelect.addEventListener('change', toggleCategoryField);
    });
</script>
{% endblock %}





