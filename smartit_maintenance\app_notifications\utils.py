from .models import Notification

def create_notification(recipient, notification_type, title, message, related_post=None, related_application=None):
    """
    Create a new notification for a user
    """
    notification = Notification.objects.create(
        recipient=recipient,
        notification_type=notification_type,
        title=title,
        message=message,
        related_post=related_post,
        related_application=related_application
    )
    return notification