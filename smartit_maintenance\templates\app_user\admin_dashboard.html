{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="mb-4">Admin Dashboard</h1>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">Total Users</h5>
                    <h2 class="card-text">{{ total_users }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">Total Specialists</h5>
                    <h2 class="card-text">{{ total_specialists }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">Total Clients</h5>
                    <h2 class="card-text">{{ total_clients }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark mb-3">
                <div class="card-body">
                    <h5 class="card-title">Total Maintenance Posts</h5>
                    <h2 class="card-text">{{ total_posts }}</h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Post Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary mb-3">
                <div class="card-body">
                    <h5 class="card-title">Open Posts</h5>
                    <h2 class="card-text text-primary">{{ open_posts }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info mb-3">
                <div class="card-body">
                    <h5 class="card-title">In Progress</h5>
                    <h2 class="card-text text-info">{{ in_progress_posts }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning mb-3">
                <div class="card-body">
                    <h5 class="card-title">Pending Approval</h5>
                    <h2 class="card-text text-warning">{{ pending_approval_posts }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success mb-3">
                <div class="card-body">
                    <h5 class="card-title">Completed</h5>
                    <h2 class="card-text text-success">{{ completed_posts }}</h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- New Support Messages Card -->
    <div class="col-md-3">
        <div class="card bg-danger text-white mb-3">
            <div class="card-body">
                <h5 class="card-title">Unread Support</h5>
                <h2 class="card-text">{{ unread_support_count }}</h2>
                <a href="{% url 'admin_support_messages' %}" class="btn btn-sm btn-light mt-2">View Messages</a>
            </div>
        </div>
    </div>
    
    <!-- Admin Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">Admin Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'admin:index' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-cog me-2"></i> Django Admin
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'admin:app_accounts_customuser_changelist' %}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-users me-2"></i> Manage Users
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'admin:app_posts_maintenancepost_changelist' %}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-clipboard-list me-2"></i> Manage Posts
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'admin:index' %}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar me-2"></i> Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Posts -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Recent Maintenance Posts</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in recent_posts %}
                                <tr>
                                    <td>
                                        <a href="{% url 'post_detail' post_id=post.id %}">{{ post.title }}</a>
                                    </td>
                                    <td>
                                        {% if post.status == 'open' %}
                                            <span class="badge bg-primary">Open</span>
                                        {% elif post.status == 'in_progress' %}
                                            <span class="badge bg-info">In Progress</span>
                                        {% elif post.status == 'pending_approval' %}
                                            <span class="badge bg-warning">Pending Approval</span>
                                        {% elif post.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ post.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ post.created_by.username }}</td>
                                    <td>{{ post.created_at|date:"M d, Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No posts found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'admin:app_posts_maintenancepost_changelist' %}" class="btn btn-primary">View All Posts</a>
                </div>
            </div>
        </div>
        
        <!-- Recent Users -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Recent Users</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Type</th>
                                    <th>Email</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                <tr>
                                    <td>
                                        <a href="{% url 'admin:app_accounts_customuser_change' user.id %}">{{ user.username }}</a>
                                    </td>
                                    <td>
                                        {% if user.user_type == 'admin' %}
                                            <span class="badge bg-danger">Admin</span>
                                        {% elif user.user_type == 'specialist' %}
                                            <span class="badge bg-success">Specialist</span>
                                        {% else %}
                                            <span class="badge bg-primary">Client</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.date_joined|date:"M d, Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No users found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'admin:app_accounts_customuser_changelist' %}" class="btn btn-success">View All Users</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
