"""
Django settings for smartit_maintenance project.

Generated by 'django-admin startproject' using Django 4.2.20.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os  # Add this import
import pymysql

# Configure PyMySQL to work with Django
pymysql.install_as_MySQLdb()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-2+#^0b-c2(us6cosm5ktz!d_ntb0h2_0h9a3q%es-vtyr+3uzx'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'smartit_maintenance.apps.SmartITAdminConfig',  # Custom admin config
    # 'django.contrib.admin',  # Comment out the default admin
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # Project apps
    'smartit_maintenance.apps.SmartITMaintenanceConfig',
    'app_accounts',
    'app_home',
    'app_posts',
    'app_specialist',
    'app_user',
    'app_notifications',
    'app_reports',
]

# Add these to your existing settings
AUTH_USER_MODEL = 'app_accounts.CustomUser'

# Authentication settings
LOGIN_URL = 'login'
LOGIN_REDIRECT_URL = 'dashboard_redirect'
LOGOUT_REDIRECT_URL = 'home'

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files (Uploaded files)
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'smartit_maintenance.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'app_notifications.context_processors.notification_count',
            ],
            'builtins': [
                'django.templatetags.static',
                'django.template.defaultfilters',
                'django.template.defaulttags',
            ],
        },
    },
]

WSGI_APPLICATION = 'smartit_maintenance.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'smartit_maintenance',
        'USER': 'root',  
        'PASSWORD': 'briannanty',  
        'HOST': 'localhost',
        'PORT': '3308',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        }
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Beem Africa API Settings
API_KEY = "9e697a2f63a1f4aa"  # Your Beem Africa API key
SECRET_KEY_BEEM = "YTUzY2RjN2RlNGQ0ODJiMjlhZWRjNDRlYTVmZDI4MjQxOTliNmJlMWRlN2YzYzE5ZDNmMzU2MTg2MTUyZTFkYQ=="  # Your Beem Africa secret key
SENDER_ID = 'SIMA Portal'  # Make sure this matches your registered sender name

# SMS Debug Mode - Set to False to send actual SMS
SMS_DEBUG_MODE = False  # Set to False to send actual SMS

# Support message notifications
SMS_NOTIFICATIONS_ENABLED = True
  # Set to True to enable SMS notifications for support messages


# Email settings
# For development/testing - emails will be printed to console
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

EMAIL_HOST = 'smtp.gmail.com'  # Or your email provider's SMTP server
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # Replace with your email
EMAIL_HOST_PASSWORD = 'hakn eaan hypn yrwq'  # Replace with your app password
DEFAULT_FROM_EMAIL = 'Smart-IT Maintenance <<EMAIL>>'


# Celery Configuration
try:
    from celery.schedules import crontab
    
    # Celery settings
    CELERY_BROKER_URL = 'redis://localhost:6379/0'
    CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
    CELERY_ACCEPT_CONTENT = ['json']
    CELERY_TASK_SERIALIZER = 'json'
    CELERY_RESULT_SERIALIZER = 'json'
    CELERY_TIMEZONE = TIME_ZONE

    # Celery Beat Schedule
    CELERY_BEAT_SCHEDULE = {
        'close-expired-posts': {
            'task': 'app_posts.tasks.task_close_expired_posts',
            'schedule': crontab(minute='*/15'),  # Run every 15 minutes
        },
    }
    
    USE_CELERY = True
except ImportError:
    # Celery not installed, use alternative scheduling method
    USE_CELERY = False
    print("Celery not installed. Scheduled tasks will not run automatically.")




# Email settings
# For development/testing - emails will be printed to console
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

EMAIL_HOST = 'smtp.gmail.com'  # Or your email provider's SMTP server
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # Replace with your email
EMAIL_HOST_PASSWORD = 'hakn eaan hypn yrwq'  # Replace with your app password
DEFAULT_FROM_EMAIL = 'Smart-IT Maintenance <<EMAIL>>'






























