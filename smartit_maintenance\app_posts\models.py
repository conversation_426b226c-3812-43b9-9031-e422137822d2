from django.db import models
from app_accounts.models import CustomUser
from django.utils import timezone

def get_default_deadline():
    """Return default deadline (7 days from now)"""
    return timezone.now() + timezone.timedelta(days=7)

class MaintenancePost(models.Model):
    STATUS_CHOICES = (
        ('open', 'Open'),
        ('pending_confirmation', 'Pending Confirmation'),
        ('in_progress', 'In Progress'),
        ('pending_approval', 'Pending Approval'),
        ('completed', 'Completed'),
        ('closed', 'Closed'),
    )
    
    EXPERIENCE_CHOICES = (
        ('1_year', '1 Year'),
        ('2_years', '2 Years'),
        ('3_years', '3 Years'),
        ('4_years', '4 Years'),
        ('5_years', '5 Years'),
    )
    
    REQUESTER_TYPE_CHOICES = (
        ('individual', 'Individual'),
        ('organization', 'Organization'),
    )
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    category = models.Char<PERSON>ield(max_length=20, choices=CustomUser.CATEGORY_CHOICES)
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_posts')
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    experience = models.CharField(max_length=20, choices=EXPERIENCE_CHOICES, default='1_year')
    deadline = models.DateTimeField(default=get_default_deadline)
    location = models.CharField(max_length=200, blank=True, help_text="Enter the location where maintenance is needed")
    requester_type = models.CharField(max_length=20, choices=REQUESTER_TYPE_CHOICES, default='individual')
    organization_name = models.CharField(max_length=200, blank=True, null=True)
    organization_department = models.CharField(max_length=200, blank=True, null=True)
    assigned_to = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, related_name='assigned_posts', null=True, blank=True)
    
    def __str__(self):
        return self.title
    
    def is_expired(self):
        """Check if the post has reached its deadline"""
        from django.utils import timezone
        return self.deadline < timezone.now()
    
    class Meta:
        db_table = 'maintenance_posts'
        ordering = ['-created_at']
        get_latest_by = 'created_at'

class SpecialistApplication(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('confirmed', 'Confirmed'),
        ('declined', 'Declined'),
        ('rejected', 'Rejected'),
    )
    
    post = models.ForeignKey(MaintenancePost, on_delete=models.CASCADE, related_name='applications')
    specialist = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='applications')
    application_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    message = models.TextField(blank=True)
    cv_file = models.FileField(upload_to='specialist_cvs/', blank=True, null=True)
    business_license = models.FileField(upload_to='business_licenses/', blank=True, null=True)
    
    class Meta:
        db_table = 'specialist_applications'
        unique_together = ('post', 'specialist')
    
    def __str__(self):
        return f"{self.specialist.username} application for {self.post.title}"

class SpecialistRating(models.Model):
    """Model for storing ratings given to specialists after job completion"""
    specialist = models.ForeignKey('app_accounts.CustomUser', on_delete=models.CASCADE, related_name='ratings_received')
    post = models.ForeignKey('MaintenancePost', on_delete=models.CASCADE, related_name='specialist_ratings')
    rated_by = models.ForeignKey('app_accounts.CustomUser', on_delete=models.CASCADE, related_name='ratings_given')
    rating = models.IntegerField(choices=[(1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')])
    comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'specialist_ratings'
        unique_together = ('specialist', 'post')  # One rating per specialist per post
        
    def __str__(self):
        return f"Rating for {self.specialist.username} on {self.post.title}: {self.rating}/5"



