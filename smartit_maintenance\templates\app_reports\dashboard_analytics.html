{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <h2 class="my-4">
        <i class="fas fa-chart-bar me-2"></i>Analytics Dashboard
    </h2>
    
    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                    <a href="{% url 'generate_report' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-pdf me-1"></i>Generate Report
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'export_data' %}?type=posts">Posts CSV</a></li>
                            <li><a class="dropdown-item" href="{% url 'export_data' %}?type=specialists">Specialists CSV</a></li>
                            <li><a class="dropdown-item" href="{% url 'export_data' %}?type=applications">Applications CSV</a></li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Key Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_posts }}</h3>
                    <p class="mb-0">Total Posts</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.completed_jobs }}</h3>
                    <p class="mb-0">Completed</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.active_posts }}</h3>
                    <p class="mb-0">Active Posts</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_specialists }}</h3>
                    <p class="mb-0">Specialists</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_users }}</h3>
                    <p class="mb-0">Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h3>{{ stats.total_applications }}</h3>
                    <p class="mb-0">Applications</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Recent Activity (7 days)</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>New Posts:</span>
                        <strong>{{ recent_posts }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>New Applications:</span>
                        <strong>{{ recent_applications }}</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>New Users:</span>
                        <strong>{{ recent_users }}</strong>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Posts by Status</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Posts by Category</h6>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Monthly Trends</h6>
                </div>
                <div class="card-body">
                    <canvas id="trendsChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Top Specialists -->
    {% if top_specialists %}
    <div class="card">
        <div class="card-header bg-light">
            <h6 class="mb-0">Top Rated Specialists</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Specialist</th>
                            <th>Category</th>
                            <th>Average Rating</th>
                            <th>Total Reviews</th>
                            <th>Completed Jobs</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in top_specialists %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if item.specialist.profile_picture %}
                                        <img src="{{ item.specialist.get_profile_picture_base64 }}" alt="Profile" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                            <i class="fas fa-user text-white small"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ item.specialist.get_full_name|default:item.specialist.username }}</strong>
                                        <br><small class="text-muted">@{{ item.specialist.username }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if item.specialist.category %}
                                    <span class="badge bg-primary">{{ item.specialist.get_category_display }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% include 'includes/rating_stars.html' with rating=item.rating show_text=True size='sm' %}
                            </td>
                            <td>{{ item.total_ratings }}</td>
                            <td>{{ item.completed_jobs }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Posts by Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
fetch('{% url "api_chart_data" %}?type=posts_by_status')
    .then(response => response.json())
    .then(data => {
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.backgroundColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });

// Posts by Category Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
fetch('{% url "api_chart_data" %}?type=posts_by_category')
    .then(response => response.json())
    .then(data => {
        new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Posts',
                    data: data.data,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });

// Monthly Trends Chart
const trendsCtx = document.getElementById('trendsChart').getContext('2d');
const monthlyData = {{ monthly_data|safe }};

new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'Posts',
            data: monthlyData.map(item => item.posts),
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'Applications',
            data: monthlyData.map(item => item.applications),
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
