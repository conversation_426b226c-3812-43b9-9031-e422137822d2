{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">User Dashboard</h2>
    
    <!-- Add this section at the top of the dashboard for pending approval posts -->
    {% for post in page_obj %}
        {% if post.status == 'pending_approval' %}
            <div class="card mb-4 border-warning">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-circle me-2"></i>Action Required: Maintenance Pending Approval</h5>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ post.title }}</h5>
                    <p class="card-text">{{ post.description|truncatewords:20 }}</p>
                    <p><strong>Specialist:</strong> {{ post.assigned_to.get_full_name|default:post.assigned_to.username }}</p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <a href="{% url 'post_detail' post.id %}" class="btn btn-outline-primary">View Details</a>
                        <div>
                            <a href="{% url 'approve_job' post.id %}" class="btn btn-success" onclick="return confirm('Are you sure you want to approve this job? This will mark the maintenance as completed.')">
                                <i class="fas fa-check-circle me-1"></i> Approve
                            </a>
                            <a href="{% url 'reject_job' post.id %}" class="btn btn-danger ms-2" onclick="return confirm('Are you sure you want to request additional changes? This will move the job back to in-progress status.')">
                                <i class="fas fa-times-circle me-1"></i> Request Changes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}

    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='dashboard' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">Your Maintenance Posts</h4>
                            <a href="{% url 'create_post' %}" class="btn btn-light btn-sm">Create New Post</a>
                        </div>
                        <div class="card-body">
                            {% if page_obj %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for post in page_obj %}
                                                <tr>
                                                    <td>{{ post.title }}</td>
                                                    <td><span class="badge bg-info">{{ post.get_category_display }}</span></td>
                                                    <td><span class="badge bg-{% if post.status == 'open' %}warning{% elif post.status == 'in_progress' %}primary{% elif post.status == 'completed' %}success{% else %}secondary{% endif %}">
                                                        {{ post.get_status_display }}
                                                    </span></td>
                                                    <td>{{ post.created_at|date:"M d, Y" }}</td>
                                                    <td>
                                                        <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                {% if page_obj.has_other_pages %}
                                <div class="pagination mt-3">
                                    <ul class="pagination">
                                        {% if page_obj.has_previous %}
                                            <li class="page-item">
                                                <a class="page-link" href="{% querystring page=1 %}">&laquo; first</a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="{% querystring page=page_obj.previous_page_number %}">previous</a>
                                            </li>
                                        {% endif %}
                                        
                                        <li class="page-item active">
                                            <span class="page-link">
                                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                            </span>
                                        </li>
                                        
                                        {% if page_obj.has_next %}
                                            <li class="page-item">
                                                <a class="page-link" href="{% querystring page=page_obj.next_page_number %}">next</a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="{% querystring page=page_obj.paginator.num_pages %}">last &raquo;</a>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </div>
                                {% endif %}
                            {% else %}
                                <p>You haven't created any maintenance posts yet.</p>
                                <a href="{% url 'create_post' %}" class="btn btn-primary">Create Your First Post</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h4>Quick Stats</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6>Total Posts</h6>
                                <h3>{{ page_obj.paginator.count }}</h3>
                            </div>
                            <div class="mb-3">
                                <h6>Pending</h6>
                                <h3>{{ pending_count }}</h3>
                            </div>
                            <div>
                                <h6>In Progress</h6>
                                <h3>{{ in_progress_count }}</h3>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Removed the Your Profile card -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



