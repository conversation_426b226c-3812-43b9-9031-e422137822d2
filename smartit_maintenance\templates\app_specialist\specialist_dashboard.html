{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Specialist Dashboard</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='dashboard' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">Applications</h5>
                            <h2 class="display-4">{{ applications_count }}</h2>
                            <p class="card-text">Total applications submitted</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">Active Jobs</h5>
                            <h2 class="display-4">{{ active_jobs_count }}</h2>
                            <p class="card-text">Jobs currently in progress</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">Completed</h5>
                            <h2 class="display-4">{{ completed_jobs_count }}</h2>
                            <p class="card-text">Successfully completed jobs</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Applications -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0 text-body-emphasis">Recent Applications</h5>
                </div>
                <div class="card-body">
                    {% if recent_applications %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Post Title</th>
                                        <th>Status</th>
                                        <th>Date Applied</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for application in recent_applications %}
                                    <tr>
                                        <td>{{ application.post.title }}</td>
                                        <td>
                                            {% if application.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif application.status == 'accepted' %}
                                                <span class="badge bg-success">Accepted</span>
                                            {% elif application.status == 'rejected' %}
                                                <span class="badge bg-danger">Rejected</span>
                                            {% elif application.status == 'completed' %}
                                                <span class="badge bg-info">Completed</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ application.application_date|date:"M d, Y" }}</td>
                                        <td>
                                            <a href="{% url 'post_detail' application.post.id %}" class="btn btn-sm btn-outline-primary">View Details</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'my_applications' %}" class="btn btn-outline-primary">View All Applications</a>
                        </div>
                    {% else %}
                        <p class="text-muted">You haven't applied to any maintenance requests yet.</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Available Posts -->
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Available Maintenance Requests</h5>
                        {% if page_obj.paginator.count > 0 %}
                        <small class="text-muted">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} requests
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if available_posts %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Posted</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for post in available_posts %}
                                    <tr>
                                        <td>{{ post.title }}</td>
                                        <td>{{ post.get_category_display }}</td>
                                        <td>{{ post.location }}</td>
                                        <td>{{ post.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            <a href="{% url 'post_detail' post.id %}" class="btn btn-sm btn-outline-primary">View Details</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Available requests pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}

                    {% else %}
                        <p class="text-muted">No maintenance requests available in your category at this time.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
