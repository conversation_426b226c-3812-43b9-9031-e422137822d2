{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'css/admin_custom.css' %}">
<style>
    /* Improved layout and spacing */
    .admin-dashboard {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }
    
    /* Container width control */
    #content {
        width: 100%;
        box-sizing: border-box;
        padding: 0;
    }
    
    /* Responsive grid layouts with better width control */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .activity-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .app-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .quick-actions-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .activity-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .app-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(1, 1fr);
        }
        
        .quick-actions-grid {
            grid-template-columns: repeat(1, 1fr);
        }
        
        .activity-grid {
            grid-template-columns: repeat(1, 1fr);
        }
        
        .app-grid {
            grid-template-columns: repeat(1, 1fr);
        }
    }
    
    /* Card styling */
    .dashboard-card {
        background-color: var(--admin-card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: none;
        transition: border-color 0.3s ease;
        height: 100%;
        border: 1px solid var(--admin-border-color);
    }
    
    .dashboard-card:hover {
        border-color: var(--primary-color);
    }
    
    .dashboard-card .card-header {
        background-color: var(--admin-module-header-bg);
        color: var(--admin-module-header-text);
        font-weight: 600;
        border-bottom: 1px solid var(--admin-border-color);
        padding: 12px 15px;
    }
    
    .dashboard-card .card-body {
        background-color: var(--admin-card-bg);
        color: var(--admin-text);
        padding: 15px;
    }
    
    /* Stats cards */
    .stat-card {
        text-align: center;
        padding: 25px 15px;
        border: 1px solid var(--admin-border-color);
        border-radius: 8px;
        background-color: var(--admin-card-bg);
        color: var(--admin-text);
        box-shadow: none;
        height: 100%;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(58, 12, 163, 0.05));
        z-index: 0;
    }
    
    .stat-icon, .stat-value, .stat-label {
        position: relative;
        z-index: 1;
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover .stat-icon {
        transform: scale(1.2);
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: var(--admin-text);
    }
    
    .stat-label {
        font-size: 1rem;
        color: var(--secondary-color);
    }
    
    /* Colorful icons */
    .users-icon i {
        color: var(--primary-color);
    }
    
    .posts-icon i {
        color: var(--secondary-color);
    }
    
    .support-icon i {
        color: var(--danger-color);
    }
    
    /* Quick actions */
    .quick-action {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        border-radius: 8px;
        background-color: var(--admin-card-bg);
        border: 1px solid var(--admin-border-color);
        transition: background-color 0.3s ease;
        height: 100%;
        color: var(--admin-text);
        box-shadow: none;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .quick-action::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(58, 12, 163, 0.05));
        z-index: -1;
    }
    
    .quick-action i {
        font-size: 2rem;
        margin-bottom: 10px;
        transition: transform 0.3s ease;
    }
    
    .quick-action:hover i {
        transform: scale(1.2);
    }
    
    .quick-action:hover {
        background-color: var(--primary-color);
        color: white;
    }
    
    /* Recent activity list */
    .activity-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .activity-item {
        padding: 12px 15px;
        border-bottom: 1px solid var(--admin-border-color);
        transition: background-color 0.3s ease;
    }
    
    .activity-item:hover {
        background-color: rgba(67, 97, 238, 0.05);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-meta {
        font-size: 0.8rem;
        color: var(--secondary-color);
    }
    
    /* App modules */
    .app-item {
        background-color: var(--admin-card-bg);
        border-radius: 8px;
        border: 1px solid var(--admin-border-color);
        overflow: hidden;
        height: 100%;
    }
    
    .app-header {
        background-color: var(--admin-module-header-bg);
        color: var(--admin-module-header-text);
        padding: 12px 15px;
        font-weight: 600;
    }
    
    .app-models {
        padding: 0;
        list-style: none;
        margin: 0;
    }
    
    .app-model-item {
        padding: 12px 15px;
        border-bottom: 1px solid var(--admin-border-color);
        transition: background-color 0.3s ease;
    }
    
    .app-model-item:hover {
        background-color: rgba(67, 97, 238, 0.05);
    }
    
    .app-model-item:last-child {
        border-bottom: none;
    }
    
    .app-model-link {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-decoration: none;
        color: var(--admin-text);
    }
    
    .app-model-name {
        font-weight: 500;
    }
    
    .app-model-count {
        font-size: 0.8rem;
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 3px 10px;
        border-radius: 12px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block coltype %}colMS{% endblock %}

{% block bodyclass %}{{ block.super }} dashboard{% endblock %}

{% block content %}
<div class="admin-dashboard">
    <!-- Stats Section -->
    <h2 class="section-title">
        <i class="fas fa-chart-line"></i>Dashboard Statistics
    </h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon users-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value">{{ total_users }}</div>
            <div class="stat-label">Total Users</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon posts-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="stat-value">{{ total_posts }}</div>
            <div class="stat-label">Maintenance Posts</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon support-icon">
                <i class="fas fa-headset"></i>
            </div>
            <div class="stat-value">{{ total_support }}</div>
            <div class="stat-label">Support Messages</div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <h2 class="section-title">
        <i class="fas fa-bolt"></i>Quick Actions
    </h2>
    
    <div class="quick-actions-grid">
        <a href="{% url 'admin:app_accounts_customuser_add' %}" class="text-decoration-none">
            <div class="quick-action">
                <i class="fas fa-user-plus"></i>
                <span>Add New User</span>
            </div>
        </a>
        
        <a href="{% url 'admin:app_posts_maintenancepost_add' %}" class="text-decoration-none">
            <div class="quick-action">
                <i class="fas fa-plus-circle"></i>
                <span>Create Post</span>
            </div>
        </a>
        
        <a href="{% url 'admin:app_home_supportmessage_changelist' %}?is_resolved__exact=0" class="text-decoration-none">
            <div class="quick-action">
                <i class="fas fa-headset"></i>
                <span>View Support</span>
            </div>
        </a>
        
        <a href="{% url 'admin:app_posts_maintenancepost_changelist' %}" class="text-decoration-none">
            <div class="quick-action">
                <i class="fas fa-clipboard-list"></i>
                <span>Manage Posts</span>
            </div>
        </a>
    </div>
    
    <!-- Recent Activity -->
    <h2 class="section-title">
        <i class="fas fa-history"></i>Recent Activity
    </h2>
    
    <div class="activity-grid">
        <!-- Recent Users -->
        <div class="dashboard-card">
            <div class="card-header">
                <i class="fas fa-users me-2"></i>New Users
            </div>
            <div class="card-body">
                {% if recent_users %}
                <ul class="activity-list">
                    {% for user in recent_users %}
                    <li class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ user.username }}</div>
                            <div class="activity-meta">Joined {{ user.date_joined|date:"M d, Y" }}</div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted">No recent users.</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Recent Posts -->
        <div class="dashboard-card">
            <div class="card-header">
                <i class="fas fa-clipboard-list me-2"></i>Recent Posts
            </div>
            <div class="card-body">
                {% if recent_posts %}
                <ul class="activity-list">
                    {% for post in recent_posts %}
                    <li class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ post.title }}</div>
                            <div class="activity-meta">Posted {{ post.created_at|date:"M d, Y" }}</div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted">No recent posts.</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Recent Support Messages -->
        <div class="dashboard-card">
            <div class="card-header">
                <i class="fas fa-headset me-2"></i>Support Messages
            </div>
            <div class="card-body">
                {% if recent_support %}
                <ul class="activity-list">
                    {% for message in recent_support %}
                    <li class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ message.subject }}</div>
                            <div class="activity-meta">{{ message.created_at|date:"M d, Y" }}</div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted">No recent support messages.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- App Modules -->
    <h2 class="section-title">
        <i class="fas fa-th-large"></i>App Modules
    </h2>
    
    <div class="app-grid">
        {% for app in app_list %}
        <div class="app-item">
            <div class="app-header">
                {% if app.app_url %}
                <a href="{{ app.app_url }}" class="text-decoration-none text-reset">{{ app.name }}</a>
                {% else %}
                {{ app.name }}
                {% endif %}
            </div>
            <ul class="app-models">
                {% for model in app.models %}
                <li class="app-model-item">
                    {% if model.admin_url %}
                    <a href="{{ model.admin_url }}" class="app-model-link">
                        <span class="app-model-name">{{ model.name }}</span>
                        {% if model.count is not None %}
                        <span class="app-model-count">{{ model.count }}</span>
                        {% endif %}
                    </a>
                    {% else %}
                    <span class="app-model-name">{{ model.name }}</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ul>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}






