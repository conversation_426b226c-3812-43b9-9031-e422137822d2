{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">My Support Messages</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='support_messages' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            {% if support_messages %}
                {% for message in support_messages %}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header {% if message.is_resolved %}bg-success{% else %}bg-primary{% endif %} text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ message.subject }}</h5>
                            <span class="badge {% if message.is_resolved %}bg-light text-dark{% else %}bg-warning{% endif %}">
                                {% if message.is_resolved %}Resolved{% else %}Pending{% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="text-muted">Your message ({{ message.created_at|date:"F j, Y, g:i a" }}):</h6>
                                <p class="mb-0">{{ message.message|linebreaks }}</p>
                            </div>
                            
                            {% if message.admin_reply %}
                                <hr>
                                <div>
                                    <h6 class="text-muted">Admin reply ({{ message.replied_at|date:"F j, Y, g:i a" }}):</h6>
                                    <p class="mb-0">{{ message.admin_reply|linebreaks }}</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-info">
                    <p class="mb-0">You haven't sent any support messages yet. If you need help, please visit the <a href="{% url 'help_support' %}">Help & Support</a> page.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}