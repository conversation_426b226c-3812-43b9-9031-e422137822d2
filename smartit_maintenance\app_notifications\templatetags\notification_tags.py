from django import template

register = template.Library()

@register.simple_tag
def get_unread_support_count(user):
    """
    Returns the count of unread support messages for an admin user
    """
    if user.is_authenticated and hasattr(user, 'user_type') and user.user_type == 'admin':
        try:
            return user.notifications.filter(
                notification_type='new_support_message',
                is_read=False
            ).count()
        except Exception:
            pass
    return 0

@register.simple_tag
def get_recent_notifications(user, limit=3):
    """
    Returns recent notifications for a user (limited to 3 by default)
    """
    if user.is_authenticated:
        try:
            from app_notifications.models import Notification
            return Notification.objects.filter(
                recipient=user
            ).order_by('-created_at')[:limit]
        except Exception:
            pass
    return []