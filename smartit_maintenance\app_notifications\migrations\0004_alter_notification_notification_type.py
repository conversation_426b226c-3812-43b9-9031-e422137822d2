# Generated by Django 5.2.3 on 2025-07-06 22:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_notifications', '0003_alter_notification_notification_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('application_status', 'Application Status Change'), ('post_status', 'Post Status Change'), ('new_application', 'New Application'), ('job_completed', 'Job Completed'), ('job_approved', 'Job Approved'), ('job_rejected', 'Job Rejected'), ('new_post', 'New Maintenance Request'), ('post_updated', 'Maintenance Request Updated'), ('system', 'System Notification'), ('support_reply', 'Support Request Reply'), ('new_support_message', 'New Support Message'), ('new_rating', 'New Rating Received')], max_length=20),
        ),
    ]
