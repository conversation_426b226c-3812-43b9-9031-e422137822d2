// Dark Mode Enhancements for Smart-IT Maintenance System

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced theme switcher functionality
    const themeButtons = document.querySelectorAll('[data-bs-theme-value]');
    const themeIcon = document.getElementById('theme-icon');
    const htmlElement = document.documentElement;
    
    // Get stored theme or default to 'light'
    const getStoredTheme = () => localStorage.getItem('theme') || 'light';
    const setStoredTheme = theme => localStorage.setItem('theme', theme);
    
    // Get preferred theme based on system preference
    const getPreferredTheme = () => {
        const storedTheme = getStoredTheme();
        if (storedTheme) {
            return storedTheme;
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    };
    
    // Set theme on HTML element
    const setTheme = theme => {
        if (theme === 'auto') {
            const preferredTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            htmlElement.setAttribute('data-bs-theme', preferredTheme);
        } else {
            htmlElement.setAttribute('data-bs-theme', theme);
        }
        updateThemeIcon(theme);
        updateActiveThemeButton(theme);
    };
    
    // Update theme icon
    const updateThemeIcon = theme => {
        if (!themeIcon) return;
        
        themeIcon.className = 'theme-icon-active me-1';
        if (theme === 'dark') {
            themeIcon.classList.add('fas', 'fa-moon');
        } else if (theme === 'light') {
            themeIcon.classList.add('fas', 'fa-sun');
        } else {
            themeIcon.classList.add('fas', 'fa-circle-half-stroke');
        }
    };
    
    // Update active theme button
    const updateActiveThemeButton = theme => {
        themeButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-bs-theme-value') === theme) {
                button.classList.add('active');
            }
        });
    };
    
    // Initialize theme on page load
    const currentTheme = getPreferredTheme();
    setTheme(currentTheme);
    setStoredTheme(currentTheme);
    
    // Add click event listeners to theme buttons
    themeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const theme = button.getAttribute('data-bs-theme-value');
            setStoredTheme(theme);
            setTheme(theme);
        });
    });
    
    // Listen for system theme changes when auto is selected
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        const storedTheme = getStoredTheme();
        if (storedTheme === 'auto') {
            setTheme('auto');
        }
    });
    
    // Fix any remaining text visibility issues
    const fixTextVisibility = () => {
        const currentTheme = htmlElement.getAttribute('data-bs-theme');
        if (currentTheme === 'dark') {
            // Find elements with potentially invisible text
            const problematicElements = document.querySelectorAll('.text-dark, .text-muted');
            problematicElements.forEach(el => {
                if (el.classList.contains('text-dark')) {
                    el.classList.add('text-body-emphasis');
                }
                if (el.classList.contains('text-muted')) {
                    el.classList.add('text-body-secondary');
                }
            });
            
            // Fix dropdown items that might be invisible
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                if (item.classList.contains('bg-light')) {
                    item.style.backgroundColor = 'var(--bs-tertiary-bg)';
                    item.style.color = 'var(--bs-body-color)';
                }
            });
        }
    };
    
    // Run text visibility fix after theme changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-bs-theme') {
                setTimeout(fixTextVisibility, 100);
            }
        });
    });
    
    observer.observe(htmlElement, {
        attributes: true,
        attributeFilter: ['data-bs-theme']
    });
    
    // Initial fix
    setTimeout(fixTextVisibility, 500);
    
    // Add smooth transitions for theme changes
    const addThemeTransitions = () => {
        const style = document.createElement('style');
        style.textContent = `
            * {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
            }
            
            .card, .dropdown-menu, .modal-content, .alert, .badge {
                transition: all 0.3s ease !important;
            }
        `;
        document.head.appendChild(style);
    };
    
    addThemeTransitions();
    
    console.log('🌙 Dark mode enhancements loaded successfully!');
});
