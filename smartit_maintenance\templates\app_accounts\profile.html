{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">Profile Settings</h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='profile' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">My Profile</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Profile Picture Section -->
                        <div class="col-md-4 mb-4 text-center">
                            {% if user.profile_picture %}
                                <img src="{{ user.get_profile_picture_base64 }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                            {% else %}
                                <div class="profile-placeholder mb-3">
                                    <i class="fas fa-user-circle fa-7x text-secondary"></i>
                                </div>
                            {% endif %}
                            
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="{{ profile_picture_form.profile_picture.id_for_label }}" class="form-label">Update Profile Picture</label>
                                    {{ profile_picture_form.profile_picture }}
                                    <button type="submit" class="btn btn-sm btn-primary mt-2">Upload</button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Profile Information Section -->
                        <div class="col-md-8">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Account Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Username:</div>
                                        <div class="col-md-8">{{ user.username }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Account Type:</div>
                                        <div class="col-md-8">
                                            <span class="badge bg-{% if user.user_type == 'admin' %}danger{% elif user.user_type == 'specialist' %}success{% else %}primary{% endif %}">
                                                {{ user.get_user_type_display }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Member Since:</div>
                                        <div class="col-md-8">{{ user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Last Login:</div>
                                        <div class="col-md-8">{{ user.last_login|date:"F d, Y H:i" }}</div>
                                    </div>
                                    {% if user.user_type == 'specialist' %}
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Specialization:</div>
                                        <div class="col-md-8">
                                            <span class="badge bg-info">{{ user.get_category_display }}</span>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{% url 'edit_profile' %}" class="btn btn-outline-primary">Edit Profile & Change Password</a>
                                    </div>
                                </div>
                            </div>
                            
                            {% if user.user_type == 'specialist' %}
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Professional Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Total Completed Jobs:</div>
                                        <div class="col-md-8">{{ completed_jobs_count|default:"0" }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Active Jobs:</div>
                                        <div class="col-md-8">{{ active_jobs_count|default:"0" }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Rating:</div>
                                        <div class="col-md-8">
                                            {% if average_rating %}
                                                <div class="rating-stars">
                                                    {% for i in "12345" %}
                                                        {% if forloop.counter <= average_rating|floatformat:"0" %}
                                                            <i class="fas fa-star text-warning"></i>
                                                        {% elif forloop.counter <= average_rating|add:"0.5"|floatformat:"0" %}
                                                            <i class="fas fa-star-half-alt text-warning"></i>
                                                        {% else %}
                                                            <i class="far fa-star text-warning"></i>
                                                        {% endif %}
                                                    {% endfor %}
                                                    <span class="ms-2">{{ average_rating|floatformat:1 }} ({{ ratings_count }} reviews)</span>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">No ratings yet</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if user.user_type == 'user' %}
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Activity Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Total Requests:</div>
                                        <div class="col-md-8">{{ total_posts_count|default:"0" }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Active Requests:</div>
                                        <div class="col-md-8">{{ active_posts_count|default:"0" }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4 fw-bold">Completed Requests:</div>
                                        <div class="col-md-8">{{ completed_posts_count|default:"0" }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Preview image before upload
    document.addEventListener('DOMContentLoaded', function() {
        const profilePictureInput = document.getElementById('{{ profile_picture_form.profile_picture.id_for_label }}');
        
        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const profileImages = document.querySelectorAll('.col-md-4.text-center img');
                    if (profileImages.length > 0) {
                        profileImages[0].src = e.target.result;
                    } else {
                        // If there's no image yet, replace the placeholder with an actual image
                        const placeholder = document.querySelector('.profile-placeholder');
                        if (placeholder) {
                            placeholder.innerHTML = `<img src="${e.target.result}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">`;
                        }
                    }
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
</script>
{% endblock %}
