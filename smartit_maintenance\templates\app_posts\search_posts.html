{% extends 'base.html' %}

{% block content %}
<div class="container">
    <h2 class="my-4">
        <i class="fas fa-search me-2"></i>Search Maintenance Requests
    </h2>
    
    <div class="row">
        <!-- Left Sidebar Navigation -->
        {% include 'includes/sidebar.html' with active_tab='search' %}
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Search Form -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Search & Filter
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" id="search-form">
                        <div class="row">
                            <!-- Search Query -->
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.search.id_for_label }}" class="form-label">Search</label>
                                {{ form.search }}
                            </div>
                            
                            <!-- Specializations Filter - Only show for non-specialists -->
                            {% if user.user_type != 'specialist' %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Available Specializations</label>
                                <div class="border rounded p-3 bg-light">
                                    {% if form.category.field.choices %}
                                        <div class="row">
                                            {% for choice_value, choice_label in form.category.field.choices %}
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="category" value="{{ choice_value }}"
                                                           id="category_{{ choice_value }}"
                                                           {% if choice_value in form.category.value|default_if_none:'' %}checked{% endif %}>
                                                    <label class="form-check-label" for="category_{{ choice_value }}">
                                                        {{ choice_label }}
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-muted mb-0">No specialists registered yet.</p>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Status Filter -->
                            <div class="{% if user.user_type == 'specialist' %}col-md-4{% else %}col-md-2{% endif %} mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status }}
                            </div>

                            <!-- Experience Filter -->
                            <div class="{% if user.user_type == 'specialist' %}col-md-4{% else %}col-md-2{% endif %} mb-3">
                                <label for="{{ form.experience.id_for_label }}" class="form-label">Experience</label>
                                {{ form.experience }}
                            </div>

                            <!-- Requester Type Filter -->
                            <div class="{% if user.user_type == 'specialist' %}col-md-4{% else %}col-md-2{% endif %} mb-3">
                                <label for="{{ form.requester_type.id_for_label }}" class="form-label">Requester Type</label>
                                {{ form.requester_type }}
                            </div>
                            
                            <!-- Date From -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.date_from.id_for_label }}" class="form-label">From Date</label>
                                {{ form.date_from }}
                            </div>
                            
                            <!-- Date To -->
                            <div class="col-md-3 mb-3">
                                <label for="{{ form.date_to.id_for_label }}" class="form-label">To Date</label>
                                {{ form.date_to }}
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Sort By -->
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.sort_by.id_for_label }}" class="form-label">Sort By</label>
                                {{ form.sort_by }}
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="col-md-8 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="{% url 'search_posts' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Search Results
                        {% if user.user_type == 'specialist' %}
                            <small class="ms-2">- {{ user.get_category_display }} Category</small>
                        {% endif %}
                    </h5>
                    <div class="text-end">
                        <span class="badge bg-light text-dark">{{ total_results }} found</span>
                        {% if page_obj.paginator.count > 0 %}
                        <br><small class="text-light">
                            Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} posts
                        </small>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if posts %}
                        <div class="row">
                            {% for post in posts %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 shadow-sm">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ post.title|truncatechars:30 }}</h6>
                                        <span class="badge bg-{% if post.status == 'open' %}success{% elif post.status == 'in_progress' %}primary{% elif post.status == 'completed' %}info{% else %}secondary{% endif %}">
                                            {{ post.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text text-muted">{{ post.description|truncatechars:100 }}</p>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-tag me-1"></i>{{ post.get_category_display }}
                                                <i class="fas fa-clock ms-2 me-1"></i>{{ post.get_experience_display }}
                                            </small>
                                        </div>
                                        {% if post.location %}
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-map-marker-alt me-1"></i>{{ post.location }}
                                            </small>
                                        </div>
                                        {% endif %}
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>{{ post.created_at|date:"M d, Y" }}
                                                <i class="fas fa-user ms-2 me-1"></i>{{ post.created_by.username }}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <a href="{% url 'post_detail' post.id %}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                        {% if user.user_type == 'specialist' and post.status == 'open' and user.category == post.category %}
                                            <a href="{% url 'apply_for_post' post.id %}" class="btn btn-success btn-sm ms-2">
                                                <i class="fas fa-paper-plane me-1"></i>Apply
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Search results pagination">
                                <ul class="pagination">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                        
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No maintenance requests found</h5>
                            <p class="text-muted">Try adjusting your search criteria or filters.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('search-form');
    const selects = form.querySelectorAll('select');
    const dateInputs = form.querySelectorAll('input[type="date"]');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
    
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            form.submit();
        });
    });
});
</script>
{% endblock %}
